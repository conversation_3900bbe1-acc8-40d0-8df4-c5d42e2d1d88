# User Invitation System

## Overview

This system implements a draft-based user invitation workflow that eliminates database constraint errors and provides a secure, self-service registration process for new users.

## How It Works

### 1. Draft Creation
When an administrator clicks "Create User":
- A draft user record is created in the `user_drafts` table
- No actual user account is created yet
- A unique signup token is generated
- The draft expires after 7 days

### 2. Invitation Process
- A signup URL is generated containing the unique token
- The administrator receives the URL to share with the invited user
- The URL format: `https://yoursite.com/signup/{token}`

### 3. User Registration
When the invited user visits the signup URL:
- The system validates the token and checks expiration
- A registration form is pre-populated with draft data
- The user can review/modify their information
- The user sets their own password
- Upon submission, the actual Supabase Auth user is created
- User profile and role records are created
- The draft is marked as completed

## Database Schema

### user_drafts Table
```sql
- id: UUID (primary key)
- email: VARCHAR(255) (not null)
- full_name: VA<PERSON>HA<PERSON>(255) (not null)
- account_number: VARCHAR(100) (optional)
- job_duties: TEXT (optional)
- organization_id: UUID (foreign key to organizations)
- facility_id: UUID (foreign key to facilities, optional)
- signup_token: UUID (unique, auto-generated)
- expires_at: TIMESTAMP (default: 7 days from creation)
- created_by: UUID (foreign key to auth.users)
- created_at: TIMESTAMP
- updated_at: TIMESTAMP
- is_active: BOOLEAN (default: true)
- invitation_sent_at: TIMESTAMP (optional)
- registered_at: TIMESTAMP (optional)
- registered_user_id: UUID (foreign key to auth.users, optional)
```

## API Endpoints

### POST /api/users/invite
Creates a new user draft and returns signup URL.

**Request:**
```json
{
  "email": "<EMAIL>",
  "full_name": "John Doe",
  "account_number": "12345",
  "job_duties": "Software Developer",
  "organization_id": "uuid",
  "facility_id": "uuid",
  "is_active": true
}
```

**Response:**
```json
{
  "success": true,
  "message": "User invitation created successfully",
  "draft_id": "uuid",
  "signup_url": "https://yoursite.com/signup/token"
}
```

### POST /api/users/complete-registration
Completes user registration from a draft.

**Request:**
```json
{
  "token": "signup-token",
  "password": "userpassword",
  "full_name": "John Doe",
  "account_number": "12345",
  "job_duties": "Software Developer"
}
```

### GET /api/users/drafts
Lists pending user drafts (for admin interface).

### DELETE /api/users/drafts/{id}
Cancels a pending invitation.

### POST /api/users/drafts/{id}
Resends an invitation (extends expiration).

## Security Features

1. **Token-based Access**: Each invitation uses a unique, unguessable UUID token
2. **Time-limited**: Invitations expire after 7 days
3. **Single Use**: Tokens become invalid after successful registration
4. **RLS Policies**: Database-level security ensures users can only access appropriate drafts
5. **Audit Trail**: Complete tracking of who invited whom and when

## Benefits

1. **No Database Constraints**: No more "null value in column id" errors
2. **Self-service**: Users control their own password creation
3. **Secure**: Time-limited, single-use invitation tokens
4. **Flexible**: Users can modify their information during registration
5. **Auditable**: Complete trail of invitation process
6. **Clean**: Automatic cleanup of expired drafts

## Usage

### For Administrators
1. Go to Settings > Users
2. Click "Create User"
3. Fill in the user details
4. Click "Send Invitation"
5. Copy the generated signup URL
6. Send the URL to the user via email or other communication

### For Invited Users
1. Click the signup URL received from administrator
2. Review pre-populated information
3. Modify details if needed
4. Set a secure password
5. Complete registration
6. Sign in with new credentials

## Environment Variables

```env
NEXT_PUBLIC_SITE_URL=https://yoursite.com
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

## Migration

To set up the system, run the migration:
```sql
-- See supabase/migrations/20241201000001_create_user_drafts.sql
```

## Cleanup

The system includes a cleanup function for expired drafts:
```sql
SELECT cleanup_expired_user_drafts();
```

This can be run manually or scheduled as a cron job.
