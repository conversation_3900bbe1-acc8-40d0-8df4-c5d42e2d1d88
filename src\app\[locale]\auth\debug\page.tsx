'use client'

import { useState, useEffect } from 'react'
import { useUser } from '@/components/auth/user-provider'
import { authService } from '@/lib/auth'
import { supabase } from '@/lib/supabase'

export default function AuthDebugPage() {
  const { user, loading: userLoading } = useUser()
  const [logs, setLogs] = useState<string[]>([])
  const [email, setEmail] = useState('<EMAIL>')
  const [password, setPassword] = useState('testpassword123')
  const [loading, setLoading] = useState(false)

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString()
    setLogs(prev => [...prev, `[${timestamp}] ${message}`])
  }

  useEffect(() => {
    addLog(`UserProvider state - Loading: ${userLoading}, User: ${user?.email || 'null'}`)
  }, [user, userLoading])

  const testSignIn = async () => {
    setLoading(true)
    setLogs([])
    
    try {
      addLog('🔐 Starting sign in test...')
      addLog(`Email: ${email}`)
      
      // Step 1: Check current session
      const { data: sessionData } = await supabase.auth.getSession()
      addLog(`Current session: ${sessionData.session ? 'Active' : 'None'}`)
      
      // Step 2: Sign out first to ensure clean state
      if (sessionData.session) {
        addLog('🚪 Signing out existing session...')
        await supabase.auth.signOut()
        addLog('✅ Signed out')
      }
      
      // Step 3: Attempt sign in
      addLog('📡 Calling supabase.auth.signInWithPassword...')
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      })
      
      if (error) {
        addLog(`❌ Sign in error: ${error.message}`)
        return
      }
      
      addLog(`✅ Sign in successful: ${data.user?.email}`)
      addLog(`Session exists: ${!!data.session}`)
      addLog(`Access token exists: ${!!data.session?.access_token}`)
      
      // Step 4: Test getCurrentUser
      addLog('🔍 Testing authService.getCurrentUser...')
      const currentUser = await authService.getCurrentUser()
      if (currentUser) {
        addLog(`✅ getCurrentUser successful: ${currentUser.email}`)
      } else {
        addLog('❌ getCurrentUser returned null')
      }
      
      // Step 5: Wait for UserProvider to update
      addLog('⏳ Waiting for UserProvider to update...')
      
    } catch (error: any) {
      addLog(`❌ Test failed: ${error.message}`)
    } finally {
      setLoading(false)
    }
  }

  const testSignOut = async () => {
    setLoading(true)
    try {
      addLog('🚪 Testing sign out...')
      await authService.signOut()
      addLog('✅ Sign out successful')
    } catch (error: any) {
      addLog(`❌ Sign out error: ${error.message}`)
    } finally {
      setLoading(false)
    }
  }

  const clearLogs = () => {
    setLogs([])
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">Authentication Debug</h1>
        
        {/* Current State */}
        <div className="bg-white shadow rounded-lg p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Current State</h2>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <strong>UserProvider Loading:</strong> {userLoading ? 'Yes' : 'No'}
            </div>
            <div>
              <strong>User:</strong> {user?.email || 'Not authenticated'}
            </div>
            <div>
              <strong>User ID:</strong> {user?.id || 'N/A'}
            </div>
            <div>
              <strong>Full Name:</strong> {user?.full_name || 'N/A'}
            </div>
          </div>
        </div>

        {/* Test Controls */}
        <div className="bg-white shadow rounded-lg p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Test Controls</h2>
          
          <div className="grid grid-cols-2 gap-4 mb-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
              <input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Password</label>
              <input
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
              />
            </div>
          </div>
          
          <div className="flex space-x-4">
            <button
              onClick={testSignIn}
              disabled={loading}
              className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 disabled:opacity-50"
            >
              {loading ? 'Testing...' : 'Test Sign In'}
            </button>
            
            <button
              onClick={testSignOut}
              disabled={loading}
              className="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600 disabled:opacity-50"
            >
              Test Sign Out
            </button>
            
            <button
              onClick={clearLogs}
              className="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600"
            >
              Clear Logs
            </button>
          </div>
        </div>
        
        {/* Logs */}
        <div className="bg-white shadow rounded-lg p-6">
          <h2 className="text-xl font-semibold mb-4">Debug Logs</h2>
          <div className="bg-gray-100 p-4 rounded max-h-96 overflow-y-auto">
            {logs.length === 0 ? (
              <p className="text-gray-500 text-sm">No logs yet. Click &quot;Test Sign In&quot; to start.</p>
            ) : (
              <div className="space-y-1">
                {logs.map((log, index) => (
                  <div key={index} className="text-sm font-mono">
                    {log}
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
