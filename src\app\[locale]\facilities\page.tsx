'use client'

import { useState, useEffect, use } from 'react'
import { useTranslations } from 'next-intl'
import { useUser } from '@/components/auth/user-provider'
import { Button } from '@/components/ui/button'
import { DataTable } from '@/components/ui/DataTable'
import { supabase, type Facility, type Organization } from '@/lib/supabase'
import toast from 'react-hot-toast'
import { PencilIcon, EyeIcon } from '@heroicons/react/24/outline'

export default function FacilitiesPage({ params }: { params: Promise<{ locale: string }> }) {
  const { locale } = use(params)
  const t = useTranslations()
  const { user } = useUser()
  const [facilities, setFacilities] = useState<(Facility & { organization: Organization })[]>([])
  const [organizations, setOrganizations] = useState<Organization[]>([])
  const [loading, setLoading] = useState(true)
  const [showCreateForm, setShowCreateForm] = useState(false)
  const [newFacility, setNewFacility] = useState({
    name: '',
    description: '',
    address: '',
    organization_id: '',
  })
  const [creating, setCreating] = useState(false)

  useEffect(() => {
    if (user) {
      fetchData()
    }
  }, [user])

  const fetchData = async () => {
    try {
      // Fetch organizations first
      const { data: orgsData, error: orgsError } = await supabase
        .from('organizations')
        .select('*')
        .order('name')

      if (orgsError) {
        if (orgsError.code === '42P01' || orgsError.message.includes('relation') || orgsError.message.includes('does not exist')) {
          console.warn('Organizations table not found. Database setup required.')
          setOrganizations([])
          setFacilities([])
          return
        }
        throw orgsError
      }
      setOrganizations(orgsData || [])

      // Fetch facilities with organization info
      const { data: facilitiesData, error: facilitiesError } = await supabase
        .from('facilities')
        .select(`
          *,
          organization:organizations(*)
        `)
        .order('created_at', { ascending: false })

      if (facilitiesError) {
        if (facilitiesError.code === '42P01' || facilitiesError.message.includes('relation') || facilitiesError.message.includes('does not exist')) {
          console.warn('Facilities table not found. Database setup required.')
          setFacilities([])
          return
        }
        throw facilitiesError
      }
      setFacilities(facilitiesData || [])
    } catch (error) {
      console.warn('Error fetching data:', error)
      setOrganizations([])
      setFacilities([])
    } finally {
      setLoading(false)
    }
  }

  const createFacility = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!user || !newFacility.name.trim() || !newFacility.organization_id) {
      toast.error('Please fill in all required fields and ensure you are logged in.')
      return
    }

    setCreating(true)
    try {
      const { error } = await supabase
        .from('facilities')
        .insert({
          name: newFacility.name.trim(),
          description: newFacility.description.trim() || null,
          address: newFacility.address.trim() || null,
          organization_id: newFacility.organization_id,
          created_by: user.id,
        })

      if (error) {
        if (error.code === '42P01' || error.message.includes('relation') || error.message.includes('does not exist')) {
          toast.error('Database setup required. Please run the database setup script first. See DATABASE_SETUP.md for instructions.')
          setCreating(false)
          return
        }
        throw error
      }

      // Refresh facilities list
      await fetchData()

      // Reset form
      setNewFacility({
        name: '',
        description: '',
        address: '',
        organization_id: '',
      })
      setShowCreateForm(false)

      toast.success('Facility created successfully!')
    } catch (error: any) {
      console.warn('Error creating facility:', error)
      toast.error(`Error creating facility: ${error.message || 'Please try again.'}`)
    } finally {
      setCreating(false)
    }
  }

  if (loading) {
    return <div className="text-center py-8">{t('common.loading')}</div>
  }

  return (
    <div className="h-full flex flex-col space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center flex-shrink-0">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            {t('facilities.title')}
          </h1>
          <p className="mt-2 text-sm text-gray-600">
            Manage facilities within your organizations.
          </p>
        </div>
        <Button
          onClick={() => setShowCreateForm(true)}
          disabled={organizations.length === 0}
        >
          {t('facilities.createFacility')}
        </Button>
      </div>

      {/* Create Facility Form */}
      {showCreateForm && (
        <div className="bg-white shadow rounded-lg p-6 flex-shrink-0">
          <h2 className="text-lg font-medium text-gray-900 mb-4">
            {t('facilities.createFacility')}
          </h2>
          <form onSubmit={createFacility} className="space-y-4">
            <div>
              <label htmlFor="organization" className="block text-sm font-medium text-gray-700">
                Organization
              </label>
              <select
                id="organization"
                required
                className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                value={newFacility.organization_id}
                onChange={(e) => setNewFacility({ ...newFacility, organization_id: e.target.value })}
              >
                <option value="">Select an organization</option>
                {organizations.map((org) => (
                  <option key={org.id} value={org.id}>
                    {org.name}
                  </option>
                ))}
              </select>
            </div>
            <div>
              <label htmlFor="facilityName" className="block text-sm font-medium text-gray-700">
                {t('facilities.facilityName')}
              </label>
              <input
                type="text"
                id="facilityName"
                required
                className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                value={newFacility.name}
                onChange={(e) => setNewFacility({ ...newFacility, name: e.target.value })}
              />
            </div>
            <div>
              <label htmlFor="facilityDescription" className="block text-sm font-medium text-gray-700">
                {t('facilities.description')}
              </label>
              <textarea
                id="facilityDescription"
                rows={3}
                className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                value={newFacility.description}
                onChange={(e) => setNewFacility({ ...newFacility, description: e.target.value })}
              />
            </div>
            <div>
              <label htmlFor="facilityAddress" className="block text-sm font-medium text-gray-700">
                {t('facilities.address')}
              </label>
              <input
                type="text"
                id="facilityAddress"
                className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                value={newFacility.address}
                onChange={(e) => setNewFacility({ ...newFacility, address: e.target.value })}
              />
            </div>
            <div className="flex space-x-3">
              <Button type="submit" disabled={creating}>
                {creating ? t('common.loading') : t('common.create')}
              </Button>
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  setShowCreateForm(false)
                  setNewFacility({
                    name: '',
                    description: '',
                    address: '',
                    organization_id: '',
                  })
                }}
              >
                {t('common.cancel')}
              </Button>
            </div>
          </form>
        </div>
      )}

      {/* Database Setup Notice */}
      {!loading && organizations.length === 0 && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6 flex-shrink-0">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-yellow-800">
                Database Setup Required
              </h3>
              <div className="mt-2 text-sm text-yellow-700">
                <p>
                  To create organizations and facilities, you need to set up the database tables first.
                  Please follow the instructions in <code className="bg-yellow-100 px-1 rounded">DATABASE_SETUP.md</code> to run the database setup script.
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Facilities List */}
      <div className="flex-1 min-h-0">
        {organizations.length === 0 ? (
          <div className="bg-white shadow rounded-lg">
            <div className="px-4 py-5 sm:p-6">
              <div className="text-center py-8">
                <p className="text-sm text-gray-500">
                  You need to create an organization first before adding facilities.
                </p>
              </div>
            </div>
          </div>
        ) : facilities.length === 0 ? (
          <div className="bg-white shadow rounded-lg">
            <div className="px-4 py-5 sm:p-6">
              <div className="text-center py-8">
                <svg
                  className="mx-auto h-12 w-12 text-gray-400"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H9m0 0H5m0 0h2M7 3h10M9 7h6m-6 4h6m-6 4h6"
                  />
                </svg>
                <h3 className="mt-2 text-sm font-medium text-gray-900">
                  {t('facilities.noFacilities')}
                </h3>
                <p className="mt-1 text-sm text-gray-500">
                  Get started by creating your first facility.
                </p>
                <div className="mt-6">
                  <Button onClick={() => setShowCreateForm(true)}>
                    {t('facilities.createFacility')}
                  </Button>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <DataTable
                data={facilities}
                fullHeight={true}
                title={t('facilities.title')}
                searchable={true}
                searchPlaceholder={`${t('common.search')} facilities...`}
                emptyMessage={t('facilities.noFacilities')}
                loading={loading}
                selectable={true}
                exportable={true}
                onExport={(data) => {
                  const csv = [
                    ['Name', 'Organization', 'Description', 'Address', 'Created Date'],
                    ...data.map(facility => [
                      facility.name,
                      facility.organization.name,
                      facility.description || '',
                      facility.address || '',
                      new Date(facility.created_at).toLocaleDateString()
                    ])
                  ].map(row => row.join(',')).join('\n')

                  const blob = new Blob([csv], { type: 'text/csv' })
                  const url = URL.createObjectURL(blob)
                  const a = document.createElement('a')
                  a.href = url
                  a.download = 'facilities.csv'
                  a.click()
                  URL.revokeObjectURL(url)
                }}
                filters={[
                  {
                    key: 'name',
                    label: 'Name',
                    type: 'text'
                  },
                  {
                    key: 'organization',
                    label: 'Organization',
                    type: 'select',
                    options: Array.from(new Set(facilities.map(f => f.organization.name)))
                      .map(name => ({ value: name, label: name }))
                  },
                  {
                    key: 'address',
                    label: 'Address',
                    type: 'text'
                  }
                ]}
                actions={[
                  {
                    label: 'Create Facility',
                    onClick: () => setShowCreateForm(true),
                    variant: 'primary'
                  }
                ]}
                columns={[
                  {
                    key: 'name',
                    header: t('facilities.facilityName'),
                    sortable: true,
                    render: (facility) => (
                      <div className="flex items-center">
                        <div>
                          <div className="text-sm font-medium text-gray-900">{facility.name}</div>
                          {facility.description && (
                            <div className="text-sm text-gray-500 max-w-xs truncate">
                              {facility.description}
                            </div>
                          )}
                        </div>
                      </div>
                    )
                  },
                  {
                    key: 'organization',
                    header: t('organizations.organization'),
                    sortable: true,
                    render: (facility) => (
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        {facility.organization.name}
                      </span>
                    )
                  },
                  {
                    key: 'address',
                    header: t('facilities.address'),
                    sortable: true,
                    render: (facility) => (
                      <div className="text-sm text-gray-500 max-w-xs truncate">
                        {facility.address || '-'}
                      </div>
                    )
                  },
                  {
                    key: 'created_at',
                    header: t('common.createdDate'),
                    sortable: true,
                    render: (facility) => (
                      <div className="text-sm text-gray-500">
                        {new Date(facility.created_at).toLocaleDateString()}
                      </div>
                    )
                  }
                ]}
                rowActions={[
                  {
                    label: t('common.edit'),
                    icon: PencilIcon,
                    onClick: (facility) => {
                      // TODO: Implement edit facility
                      console.log('Edit facility:', facility)
                    },
                    variant: 'primary'
                  },
                  {
                    label: 'View Details',
                    icon: EyeIcon,
                    onClick: (facility) => {
                      // TODO: Navigate to facility details
                      console.log('View details for:', facility)
                    }
                  }
                ]}
              />
        )}
      </div>
    </div>
  )
}
