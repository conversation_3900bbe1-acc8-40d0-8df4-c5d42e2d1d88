'use client'

import React, { useState, useEffect, use } from 'react'
import { useTranslations } from 'next-intl'
import { useUser } from '@/components/auth/user-provider'
import { Button } from '@/components/ui/button'
import { DataTable } from '@/components/ui/DataTable'
import { OrganizationForm } from '@/components/forms/OrganizationForm'
import { ConfirmDialog } from '@/components/ui/ConfirmDialog'
import { supabase, type Organization } from '@/lib/supabase'
import { authService } from '@/lib/auth'
import Link from 'next/link'
import toast from 'react-hot-toast'
import { PencilIcon, EyeIcon, TrashIcon } from '@heroicons/react/24/outline'

export default function OrganizationsPage({ params }: { params: Promise<{ locale: string }> }) {
  const { locale } = use(params)
  const t = useTranslations()
  const { user } = useUser()
  const [organizations, setOrganizations] = useState<Organization[]>([])
  const [loading, setLoading] = useState(true)

  // Modal states
  const [showOrganizationForm, setShowOrganizationForm] = useState(false)
  const [editingOrganization, setEditingOrganization] = useState<Organization | null>(null)
  const [formMode, setFormMode] = useState<'create' | 'edit'>('create')
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false)
  const [deleteTarget, setDeleteTarget] = useState<{ id: string; name: string } | null>(null)
  const [deleteLoading, setDeleteLoading] = useState(false)

  useEffect(() => {
    if (user) {
      fetchOrganizations()
    }
  }, [user])

  const fetchOrganizations = async () => {
    try {
      const { data, error } = await supabase
        .from('organizations')
        .select('*')
        .order('created_at', { ascending: false })

      if (error) {
        // Check if it's a table not found error
        if (error.code === '42P01' || error.message.includes('relation') || error.message.includes('does not exist')) {
          console.warn('Organizations table not found. Database setup required.')
          setOrganizations([])
          return
        }
        throw error
      }
      setOrganizations(data || [])
    } catch (error) {
      console.warn('Error fetching organizations:', error)
      setOrganizations([])
    } finally {
      setLoading(false)
    }
  }

  // Modal handlers
  const handleCreateOrganization = React.useCallback(() => {
    setFormMode('create')
    setEditingOrganization(null)
    setShowOrganizationForm(true)
  }, [])

  const handleEditOrganization = React.useCallback((organization: Organization) => {
    setFormMode('edit')
    setEditingOrganization(organization)
    setShowOrganizationForm(true)
  }, [])

  const handleDeleteRequest = React.useCallback((organization: Organization) => {
    setDeleteTarget({ id: organization.id, name: organization.name })
    setShowDeleteConfirm(true)
  }, [])

  const handleDeleteConfirm = React.useCallback(async () => {
    if (!deleteTarget) return

    setDeleteLoading(true)
    try {
      const { error } = await supabase
        .from('organizations')
        .delete()
        .eq('id', deleteTarget.id)

      if (error) throw error

      await fetchOrganizations()
      toast.success('Organization deleted successfully')
      setShowDeleteConfirm(false)
      setDeleteTarget(null)
    } catch (error: any) {
      toast.error(`Error deleting organization: ${error.message}`)
    } finally {
      setDeleteLoading(false)
    }
  }, [deleteTarget, fetchOrganizations])

  // Modal close handlers
  const handleCloseOrganizationForm = React.useCallback(() => setShowOrganizationForm(false), [])
  const handleCloseDeleteConfirm = React.useCallback(() => setShowDeleteConfirm(false), [])

  if (loading) {
    return <div className="text-center py-8">{t('common.loading')}</div>
  }

  return (
    <div className="h-full flex flex-col space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center flex-shrink-0">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            {t('organizations.title')}
          </h1>
          <p className="mt-2 text-sm text-gray-600">
            Manage your organizations and their settings.
          </p>
        </div>
        <Button onClick={handleCreateOrganization}>
          {t('organizations.createOrganization')}
        </Button>
      </div>



      {/* Database Setup Notice */}
      {!loading && organizations.length === 0 && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6 flex-shrink-0">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-yellow-800">
                Database Setup Required
              </h3>
              <div className="mt-2 text-sm text-yellow-700">
                <p>
                  To create organizations and facilities, you need to set up the database tables first.
                  Please follow the instructions in <code className="bg-yellow-100 px-1 rounded">DATABASE_SETUP.md</code> to run the database setup script.
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Organizations Table */}
      <div className="flex-1 min-h-0">
        <DataTable
          data={organizations}
          fullHeight={true}
          title={t('organizations.title')}
          searchable={true}
          searchPlaceholder={`${t('common.search')} organizations...`}
          emptyMessage={t('organizations.noOrganizations')}
          loading={loading}
          selectable={true}
          exportable={true}
          onExport={(data) => {
            const csv = [
              ['Name', 'Description', 'Created Date'],
              ...data.map(org => [
                org.name,
                org.description || '',
                new Date(org.created_at).toLocaleDateString()
              ])
            ].map(row => row.join(',')).join('\n')

            const blob = new Blob([csv], { type: 'text/csv' })
            const url = URL.createObjectURL(blob)
            const a = document.createElement('a')
            a.href = url
            a.download = 'organizations.csv'
            a.click()
            URL.revokeObjectURL(url)
          }}
          filters={[
            {
              key: 'name',
              label: 'Name',
              type: 'text'
            },
            {
              key: 'description',
              label: 'Description',
              type: 'text'
            }
          ]}
          actions={[
            {
              label: 'Create Organization',
              onClick: handleCreateOrganization,
              variant: 'primary'
            }
          ]}
          columns={[
            {
              key: 'name',
              header: t('organizations.organizationName'),
              sortable: true,
              render: (org) => (
                <div className="flex items-center">
                  <div>
                    <div className="text-sm font-medium text-gray-900">{org.name}</div>
                    {org.description && (
                      <div className="text-sm text-gray-500 max-w-xs truncate">
                        {org.description}
                      </div>
                    )}
                  </div>
                </div>
              )
            },
            {
              key: 'created_at',
              header: t('common.createdDate'),
              sortable: true,
              render: (org) => (
                <div className="text-sm text-gray-500">
                  {new Date(org.created_at).toLocaleDateString()}
                </div>
              )
            }
          ]}
          rowActions={[
            {
              label: t('common.edit'),
              icon: PencilIcon,
              onClick: (org) => handleEditOrganization(org),
              variant: 'primary'
            },
            {
              label: t('facilities.viewDetails'),
              icon: EyeIcon,
              onClick: (org) => {
                window.location.href = `/${locale}/organizations/${org.id}`
              }
            },
            {
              label: t('common.delete'),
              icon: TrashIcon,
              onClick: (org) => handleDeleteRequest(org),
              variant: 'danger'
            }
          ]}
          onRowClick={(org) => {
            window.location.href = `/${locale}/organizations/${org.id}`
          }}
        />
      </div>

      {/* Modal Components */}
      <OrganizationForm
        key="organization-form"
        isOpen={showOrganizationForm}
        onClose={handleCloseOrganizationForm}
        onSuccess={fetchOrganizations}
        organization={editingOrganization}
        mode={formMode}
      />

      <ConfirmDialog
        key="delete-confirm"
        isOpen={showDeleteConfirm}
        onClose={handleCloseDeleteConfirm}
        onConfirm={handleDeleteConfirm}
        title="Delete Organization"
        message={`Are you sure you want to delete "${deleteTarget?.name}"? This action cannot be undone.`}
        confirmText="Delete"
        cancelText="Cancel"
        variant="danger"
        loading={deleteLoading}
      />
    </div>
  )
}
