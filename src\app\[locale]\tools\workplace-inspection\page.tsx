'use client'

// Force dynamic rendering
export const dynamic = 'force-dynamic'

import { useState, useEffect, use } from 'react'
import { useTranslations } from 'next-intl'
import { useUser } from '@/components/auth/user-provider'
import { DataTable } from '@/components/ui/data-table'
import { Button } from '@/components/ui/button'
import { inspectionService } from '@/lib/inspection-service'
import { supabase, type InspectionPoint, type InspectionCategory, type Organization, type Facility, type Workplace } from '@/lib/supabase'
import { InspectionPointForm } from '@/components/forms/InspectionPointForm'
import { InspectionCategoryForm } from '@/components/forms/InspectionCategoryForm'
import toast from 'react-hot-toast'
import {
  PlusIcon,
  TagIcon,
  ClipboardDocumentCheckIcon,
  PencilIcon,
  TrashIcon
} from '@heroicons/react/24/outline'

export default function WorkplaceInspectionPage({ params }: { params: Promise<{ locale: string }> }) {
  const { locale } = use(params)
  const t = useTranslations()
  const { user } = useUser()

  const [loading, setLoading] = useState(true)
  const [inspectionPoints, setInspectionPoints] = useState<InspectionPoint[]>([])
  const [categories, setCategories] = useState<InspectionCategory[]>([])
  const [organizations, setOrganizations] = useState<Organization[]>([])
  const [facilities, setFacilities] = useState<Facility[]>([])
  const [workplaces, setWorkplaces] = useState<Workplace[]>([])
  
  const [showPointForm, setShowPointForm] = useState(false)
  const [showCategoryForm, setShowCategoryForm] = useState(false)
  const [editingPoint, setEditingPoint] = useState<InspectionPoint | null>(null)
  const [editingCategory, setEditingCategory] = useState<InspectionCategory | null>(null)
  const [formMode, setFormMode] = useState<'create' | 'edit'>('create')

  const [selectedOrganization, setSelectedOrganization] = useState('')
  const [selectedFacility, setSelectedFacility] = useState('')
  const [selectedWorkplace, setSelectedWorkplace] = useState('')

  useEffect(() => {
    if (user) {
      fetchData()
    }
  }, [user])

  const fetchData = async () => {
    try {
      setLoading(true)
      
      // Fetch organizations
      const { data: orgsData, error: orgsError } = await supabase
        .from('organizations')
        .select('*')
        .order('name')

      if (orgsError) throw orgsError
      setOrganizations(orgsData || [])

      // Fetch facilities
      const { data: facilitiesData, error: facilitiesError } = await supabase
        .from('facilities')
        .select('*')
        .order('name')

      if (facilitiesError) throw facilitiesError
      setFacilities(facilitiesData || [])

      // Fetch workplaces
      const { data: workplacesData, error: workplacesError } = await supabase
        .from('workplaces')
        .select('*')
        .order('name')

      if (workplacesError) throw workplacesError
      setWorkplaces(workplacesData || [])

      // Fetch categories and inspection points
      const [categoriesData, pointsData] = await Promise.all([
        inspectionService.getCategories(),
        inspectionService.getInspectionPoints()
      ])

      setCategories(categoriesData)
      setInspectionPoints(pointsData)
    } catch (error: any) {
      toast.error(`Error loading data: ${error.message}`)
    } finally {
      setLoading(false)
    }
  }

  const handleCreatePoint = () => {
    setEditingPoint(null)
    setFormMode('create')
    setShowPointForm(true)
  }

  const handleEditPoint = (point: InspectionPoint) => {
    setEditingPoint(point)
    setFormMode('edit')
    setShowPointForm(true)
  }

  const handleDeletePoint = async (point: InspectionPoint) => {
    if (!confirm('Are you sure you want to delete this inspection point?')) {
      return
    }

    try {
      await inspectionService.deleteInspectionPoint(point.id)
      toast.success('Inspection point deleted successfully')
      fetchData()
    } catch (error: any) {
      toast.error(`Error deleting inspection point: ${error.message}`)
    }
  }

  const handleCreateCategory = () => {
    setEditingCategory(null)
    setFormMode('create')
    setShowCategoryForm(true)
  }

  const handleEditCategory = (category: InspectionCategory) => {
    setEditingCategory(category)
    setFormMode('edit')
    setShowCategoryForm(true)
  }

  const handleDeleteCategory = async (category: InspectionCategory) => {
    if (!confirm('Are you sure you want to delete this category?')) {
      return
    }

    try {
      await inspectionService.deleteCategory(category.id)
      toast.success('Category deleted successfully')
      fetchData()
    } catch (error: any) {
      toast.error(`Error deleting category: ${error.message}`)
    }
  }

  const handleClosePointForm = () => {
    setShowPointForm(false)
    setEditingPoint(null)
  }

  const handleCloseCategoryForm = () => {
    setShowCategoryForm(false)
    setEditingCategory(null)
  }

  const filteredInspectionPoints = inspectionPoints.filter(point => {
    if (selectedOrganization && point.organization_id !== selectedOrganization) return false
    if (selectedFacility && point.facility_id !== selectedFacility) return false
    if (selectedWorkplace && point.workplace_id !== selectedWorkplace) return false
    return true
  })

  const columns = [
    {
      key: 'inspection_name',
      label: 'Name',
      sortable: true
    },
    {
      key: 'description',
      label: 'Description',
      sortable: false
    },
    {
      key: 'category',
      label: 'Category',
      sortable: true,
      render: (point: InspectionPoint) => point.category?.name || 'N/A'
    },
    {
      key: 'workplace',
      label: 'Workplace',
      sortable: true,
      render: (point: InspectionPoint) => point.workplace?.name || 'N/A'
    },
    {
      key: 'facility',
      label: 'Facility',
      sortable: true,
      render: (point: InspectionPoint) => point.facility?.name || 'N/A'
    },
    {
      key: 'organization',
      label: 'Organization',
      sortable: true,
      render: (point: InspectionPoint) => point.organization?.name || 'N/A'
    },
    {
      key: 'is_active',
      label: 'Status',
      sortable: true,
      render: (point: InspectionPoint) => (
        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
          point.is_active 
            ? 'bg-green-100 text-green-800' 
            : 'bg-red-100 text-red-800'
        }`}>
          {point.is_active ? 'Active' : 'Inactive'}
        </span>
      )
    },
    {
      key: 'created_at',
      label: 'Created',
      sortable: true,
      render: (point: InspectionPoint) => new Date(point.created_at).toLocaleDateString()
    }
  ]

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg text-gray-600">Loading...</div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Workplace Inspection Management</h1>
          <p className="mt-1 text-sm text-gray-500">
            Manage inspection points and categories for workplace safety inspections
          </p>
        </div>
        <div className="flex space-x-3">
          <Button
            onClick={handleCreateCategory}
            variant="outline"
            className="flex items-center space-x-2"
          >
            <TagIcon className="h-4 w-4" />
            <span>Manage Categories</span>
          </Button>
          <Button
            onClick={handleCreatePoint}
            className="flex items-center space-x-2"
          >
            <PlusIcon className="h-4 w-4" />
            <span>Create Inspection Point</span>
          </Button>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white p-4 rounded-lg shadow border">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Filters</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Organization
            </label>
            <select
              value={selectedOrganization}
              onChange={(e) => {
                setSelectedOrganization(e.target.value)
                setSelectedFacility('')
                setSelectedWorkplace('')
              }}
              className="block w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-orange-500 focus:outline-none focus:ring-orange-500"
            >
              <option value="">All Organizations</option>
              {organizations.map((org) => (
                <option key={org.id} value={org.id}>
                  {org.name}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Facility
            </label>
            <select
              value={selectedFacility}
              onChange={(e) => {
                setSelectedFacility(e.target.value)
                setSelectedWorkplace('')
              }}
              disabled={!selectedOrganization}
              className="block w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-orange-500 focus:outline-none focus:ring-orange-500 disabled:bg-gray-100"
            >
              <option value="">All Facilities</option>
              {facilities
                .filter(facility => !selectedOrganization || facility.organization_id === selectedOrganization)
                .map((facility) => (
                  <option key={facility.id} value={facility.id}>
                    {facility.name}
                  </option>
                ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Workplace
            </label>
            <select
              value={selectedWorkplace}
              onChange={(e) => setSelectedWorkplace(e.target.value)}
              disabled={!selectedFacility}
              className="block w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-orange-500 focus:outline-none focus:ring-orange-500 disabled:bg-gray-100"
            >
              <option value="">All Workplaces</option>
              {workplaces
                .filter(workplace => !selectedFacility || workplace.facility_id === selectedFacility)
                .map((workplace) => (
                  <option key={workplace.id} value={workplace.id}>
                    {workplace.name}
                  </option>
                ))}
            </select>
          </div>
        </div>
      </div>

      {/* Data Table */}
      <DataTable
        data={filteredInspectionPoints}
        columns={columns}
        rowActions={[
          {
            label: 'Edit',
            icon: PencilIcon,
            onClick: handleEditPoint
          },
          {
            label: 'Delete',
            icon: TrashIcon,
            onClick: handleDeletePoint
          }
        ]}
        fullHeight
      />

      {/* Forms */}
      <InspectionPointForm
        isOpen={showPointForm}
        onClose={handleClosePointForm}
        onSuccess={fetchData}
        inspectionPoint={editingPoint}
        organizations={organizations}
        facilities={facilities}
        workplaces={workplaces}
        categories={categories}
        mode={formMode}
      />

      <InspectionCategoryForm
        isOpen={showCategoryForm}
        onClose={handleCloseCategoryForm}
        onSuccess={fetchData}
        category={editingCategory}
        organizations={organizations}
        mode={formMode}
      />
    </div>
  )
}
