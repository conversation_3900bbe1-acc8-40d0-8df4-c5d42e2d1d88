'use client'

import { useState, useEffect, use } from 'react'
import { useTranslations } from 'next-intl'
import { useUser } from '@/components/auth/user-provider'
import { Button } from '@/components/ui/button'
import { DataTable } from '@/components/ui/DataTable'
import { supabase } from '@/lib/supabase'
import toast from 'react-hot-toast'
import { UserIcon, UserPlusIcon, UserMinusIcon, KeyIcon, PencilIcon } from '@heroicons/react/24/outline'

interface User {
  id: string
  email: string
  full_name?: string
  account_number?: string
  job_duties?: string
  is_active?: boolean
  created_at: string
  organization?: {
    id: string
    name: string
  }
}

export default function UsersPage({ params }: { params: Promise<{ locale: string }> }) {
  const { locale } = use(params)
  const t = useTranslations()
  const { user } = useUser()
  const [users, setUsers] = useState<User[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (user) {
      fetchUsers()
    }
  }, [user])

  const fetchUsers = async () => {
    try {
      const { data, error } = await supabase
        .from('users')
        .select(`
          *,
          organization:organizations(id, name)
        `)
        .order('created_at', { ascending: false })

      if (error) {
        if (error.code === '42P01' || error.message.includes('relation') || error.message.includes('does not exist')) {
          console.warn('Users table not found. Database setup required.')
          setUsers([])
          return
        }
        throw error
      }
      setUsers(data || [])
    } catch (error) {
      console.warn('Error fetching users:', error)
      setUsers([])
    } finally {
      setLoading(false)
    }
  }

  const toggleUserStatus = async (userId: string, currentStatus: boolean, userName: string) => {
    try {
      const { error } = await supabase
        .from('users')
        .update({ is_active: !currentStatus })
        .eq('id', userId)

      if (error) throw error

      await fetchUsers()
      toast.success(`User ${userName} ${!currentStatus ? 'enabled' : 'disabled'} successfully`)
    } catch (error: any) {
      toast.error(`Error updating user status: ${error.message}`)
    }
  }

  const resetUserPassword = async (userId: string, email: string) => {
    try {
      // This would typically trigger a password reset email
      // For now, we'll just show a success message
      toast.success(`Password reset email sent to ${email}`)
    } catch (error: any) {
      toast.error(`Error resetting password: ${error.message}`)
    }
  }

  return (
    <div className="space-y-6 h-full flex flex-col">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            {t('users.title')}
          </h1>
          <p className="mt-2 text-sm text-gray-600">
            Manage users and their roles within your organizations.
          </p>
        </div>
        <Button>
          <UserPlusIcon className="w-4 h-4 mr-2" />
          Invite User
        </Button>
      </div>

      {/* Users Table */}
      <div className="flex-1 min-h-0">
        <DataTable
          data={users}
          fullHeight={true}
          title="Users"
          searchable={true}
          searchPlaceholder="Search users..."
          emptyMessage="No users found. Invite users to get started."
          loading={loading}
          selectable={true}
          exportable={true}
          onExport={(data) => {
            const csv = [
              ['Name', 'Email', 'Account Number', 'Job Duties', 'Organization', 'Status', 'Created Date'],
              ...data.map(user => [
                user.full_name || '',
                user.email,
                user.account_number || '',
                user.job_duties || '',
                user.organization?.name || '',
                user.is_active ? 'Active' : 'Inactive',
                new Date(user.created_at).toLocaleDateString()
              ])
            ].map(row => row.join(',')).join('\n')

            const blob = new Blob([csv], { type: 'text/csv' })
            const url = URL.createObjectURL(blob)
            const a = document.createElement('a')
            a.href = url
            a.download = 'users.csv'
            a.click()
            URL.revokeObjectURL(url)
          }}
          filters={[
            {
              key: 'full_name',
              label: 'Name',
              type: 'text'
            },
            {
              key: 'email',
              label: 'Email',
              type: 'text'
            },
            {
              key: 'is_active',
              label: 'Status',
              type: 'boolean'
            },
            {
              key: 'organization',
              label: 'Organization',
              type: 'select',
              options: Array.from(new Set(users.map(u => u.organization?.name).filter(Boolean)))
                .map(name => ({ value: name!, label: name! }))
            }
          ]}
          actions={[
            {
              label: 'Invite Users',
              onClick: () => {
                toast.success('Invite users functionality coming soon')
              },
              variant: 'primary'
            }
          ]}
          columns={[
            {
              key: 'full_name',
              header: 'User',
              sortable: true,
              render: (user) => (
                <div className="flex items-center">
                  <UserIcon className="w-5 h-5 text-gray-400 mr-2" />
                  <div>
                    <div className="text-sm font-medium text-gray-900">
                      {user.full_name || 'No name'}
                    </div>
                    <div className="text-sm text-gray-500">{user.email}</div>
                  </div>
                </div>
              )
            },
            {
              key: 'account_number',
              header: 'Account Number',
              sortable: true,
              render: (user) => user.account_number || '-'
            },
            {
              key: 'job_duties',
              header: 'Job Duties',
              sortable: true,
              render: (user) => (
                <div className="text-sm text-gray-600 max-w-xs truncate">
                  {user.job_duties || '-'}
                </div>
              )
            },
            {
              key: 'organization',
              header: 'Organization',
              sortable: true,
              render: (user) => user.organization?.name || '-'
            },
            {
              key: 'is_active',
              header: 'Status',
              sortable: true,
              render: (user) => (
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                  user.is_active
                    ? 'bg-green-100 text-green-800'
                    : 'bg-red-100 text-red-800'
                }`}>
                  {user.is_active ? 'Active' : 'Inactive'}
                </span>
              )
            },
            {
              key: 'created_at',
              header: 'Created Date',
              sortable: true,
              render: (user) => (
                <div className="text-sm text-gray-500">
                  {new Date(user.created_at).toLocaleDateString()}
                </div>
              )
            }
          ]}
          rowActions={[
            {
              label: 'Edit',
              icon: PencilIcon,
              onClick: (user) => {
                toast.success('Edit user functionality coming soon')
              },
              variant: 'primary'
            },
            {
              label: 'Toggle Status',
              icon: UserMinusIcon,
              onClick: (user) => toggleUserStatus(user.id, user.is_active || false, user.full_name || user.email),
              variant: 'secondary'
            },
            {
              label: 'Reset Password',
              icon: KeyIcon,
              onClick: (user) => resetUserPassword(user.id, user.email)
            }
          ]}
        />
      </div>
    </div>
  )
}
