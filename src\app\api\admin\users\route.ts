import { createClient } from '@supabase/supabase-js'
import { NextRequest, NextResponse } from 'next/server'

// Force dynamic rendering
export const dynamic = 'force-dynamic'

// Create clients only when needed to avoid build-time errors
function getSupabaseAdmin() {
  const url = process.env.NEXT_PUBLIC_SUPABASE_URL
  const key = process.env.SUPABASE_SERVICE_ROLE_KEY

  if (!url || !key) {
    throw new Error('Missing Supabase environment variables')
  }

  return createClient(url, key, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  })
}

function getSupabaseClient() {
  const url = process.env.NEXT_PUBLIC_SUPABASE_URL
  const key = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

  if (!url || !key) {
    throw new Error('Missing Supabase environment variables')
  }

  return createClient(url, key)
}

function generatePassword(): string {
  const length = 12
  const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*'
  let password = ''
  for (let i = 0; i < length; i++) {
    password += charset.charAt(Math.floor(Math.random() * charset.length))
  }
  return password
}

export async function POST(request: NextRequest) {
  try {
    const { email, full_name, account_number, job_duties, organization_id } = await request.json()

    // Get the current user from the authorization header
    const authHeader = request.headers.get('authorization')
    if (!authHeader) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Set the auth header for the regular client to check permissions
    const token = authHeader.replace('Bearer ', '')
    const supabase = getSupabaseClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser(token)
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if the current user has admin/manager permissions in the target organization
    const { data: userRole, error: roleError } = await supabase
      .from('user_roles')
      .select(`
        role:roles(name),
        organization_id
      `)
      .eq('user_id', user.id)
      .eq('organization_id', organization_id)
      .single()

    if (roleError || !userRole || !['Admin', 'Manager'].includes((userRole.role as any)?.name)) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }

    // Generate temporary password
    const tempPassword = generatePassword()

    // Create user in Supabase Auth using admin client
    const supabaseAdmin = getSupabaseAdmin()
    const { data: authData, error: authCreateError } = await supabaseAdmin.auth.admin.createUser({
      email: email.trim(),
      password: tempPassword,
      email_confirm: true,
      user_metadata: {
        full_name: full_name.trim(),
      }
    })

    if (authCreateError) {
      console.error('Auth creation error:', authCreateError)
      return NextResponse.json({ error: authCreateError.message }, { status: 400 })
    }

    if (!authData.user) {
      return NextResponse.json({ error: 'Failed to create user' }, { status: 400 })
    }

    // Create user profile using admin client (bypasses RLS)
    const { error: profileError } = await supabaseAdmin
      .from('users')
      .insert({
        id: authData.user.id,
        email: email.trim(),
        full_name: full_name.trim(),
        account_number: account_number?.trim() || null,
        job_duties: job_duties?.trim() || null,
        is_active: true,
      })

    if (profileError) {
      console.error('Profile creation error:', profileError)
      // Clean up the auth user if profile creation fails
      await supabaseAdmin.auth.admin.deleteUser(authData.user.id)
      return NextResponse.json({ error: profileError.message }, { status: 400 })
    }

    // Get the Staff role ID
    const { data: roleData, error: getRoleError } = await supabaseAdmin
      .from('roles')
      .select('id')
      .eq('name', 'Staff')
      .single()

    if (getRoleError || !roleData) {
      console.error('Role fetch error:', getRoleError)
      return NextResponse.json({ error: 'Failed to assign role' }, { status: 400 })
    }

    // Assign user to organization with Staff role using admin client
    const { error: roleAssignError } = await supabaseAdmin
      .from('user_roles')
      .insert({
        user_id: authData.user.id,
        organization_id: organization_id,
        role_id: roleData.id,
      })

    if (roleAssignError) {
      console.error('Role assignment error:', roleAssignError)
      // Clean up on failure
      await supabaseAdmin.auth.admin.deleteUser(authData.user.id)
      return NextResponse.json({ error: roleAssignError.message }, { status: 400 })
    }

    return NextResponse.json({
      success: true,
      user: authData.user,
      temporaryPassword: tempPassword
    })

  } catch (error: any) {
    console.error('User creation error:', error)
    return NextResponse.json({ error: error.message || 'Internal server error' }, { status: 500 })
  }
}

export async function PATCH(request: NextRequest) {
  try {
    const { userId, password } = await request.json()

    // Get the current user from the authorization header
    const authHeader = request.headers.get('authorization')
    if (!authHeader) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const token = authHeader.replace('Bearer ', '')
    const supabase = getSupabaseClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser(token)
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if the current user has admin/manager permissions for the target user
    const { data: targetUserRole } = await supabase
      .from('user_roles')
      .select('organization_id')
      .eq('user_id', userId)
      .single()

    if (!targetUserRole) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    const { data: currentUserRole } = await supabase
      .from('user_roles')
      .select(`
        role:roles(name),
        organization_id
      `)
      .eq('user_id', user.id)
      .eq('organization_id', targetUserRole.organization_id)
      .single()

    if (!currentUserRole || !['Admin', 'Manager'].includes((currentUserRole.role as any)?.name)) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }

    // Reset password using admin client
    const supabaseAdmin = getSupabaseAdmin()
    const { error: resetError } = await supabaseAdmin.auth.admin.updateUserById(userId, {
      password: password
    })

    if (resetError) {
      console.error('Password reset error:', resetError)
      return NextResponse.json({ error: resetError.message }, { status: 400 })
    }

    return NextResponse.json({ success: true })

  } catch (error: any) {
    console.error('Password reset error:', error)
    return NextResponse.json({ error: error.message || 'Internal server error' }, { status: 500 })
  }
}
