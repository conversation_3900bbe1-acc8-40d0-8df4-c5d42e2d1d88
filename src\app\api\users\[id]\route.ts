import { NextRequest, NextResponse } from 'next/server'
import { userInvitationService, type InviteUserData } from '@/lib/user-invitation'
import { supabase } from '@/lib/supabase'

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    
    // Verify the user is authenticated
    const authHeader = request.headers.get('authorization')
    if (!authHeader) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Extract the token from the Authorization header
    const token = authHeader.replace('Bearer ', '')
    
    // Verify the token with Supabase
    const { data: { user }, error: authError } = await supabase.auth.getUser(token)
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Invalid authentication token' },
        { status: 401 }
      )
    }

    // Parse request body
    const body = await request.json()
    const updates: Partial<InviteUserData> = {
      full_name: body.full_name,
      account_number: body.account_number,
      job_duties: body.job_duties,
      organization_id: body.organization_id,
      is_active: body.is_active
    }

    // Validate required fields
    if (!updates.full_name || !updates.organization_id) {
      return NextResponse.json(
        { error: 'Full name and organization are required' },
        { status: 400 }
      )
    }

    // TODO: Add permission check here
    // For now, we'll allow any authenticated user to update others
    // In a real app, you'd check if the user has admin/manager permissions

    // Update the user
    const result = await userInvitationService.updateUserProfile(id, updates)

    if (!result.success) {
      return NextResponse.json(
        { error: result.message },
        { status: 400 }
      )
    }

    return NextResponse.json({
      success: true,
      message: result.message
    })

  } catch (error: any) {
    console.error('Error in user update API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    
    // Verify the user is authenticated
    const authHeader = request.headers.get('authorization')
    if (!authHeader) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Extract the token from the Authorization header
    const token = authHeader.replace('Bearer ', '')
    
    // Verify the token with Supabase
    const { data: { user }, error: authError } = await supabase.auth.getUser(token)
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Invalid authentication token' },
        { status: 401 }
      )
    }

    // TODO: Add permission check here
    // TODO: Implement user deletion logic
    // This should delete from auth.users and cascade to users table

    return NextResponse.json({
      success: true,
      message: 'User deletion not yet implemented'
    })

  } catch (error: any) {
    console.error('Error in user deletion API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
