import { NextRequest, NextResponse } from 'next/server'
import { userDraftService, type CompleteRegistrationData } from '@/lib/user-draft-service'

export async function POST(request: NextRequest) {
  try {
    // Parse request body
    const body = await request.json()
    const { token, ...registrationData }: { token: string } & CompleteRegistrationData = body

    // Validate required fields
    if (!token) {
      return NextResponse.json(
        { error: 'Registration token is required' },
        { status: 400 }
      )
    }

    if (!registrationData.password) {
      return NextResponse.json(
        { error: 'Password is required' },
        { status: 400 }
      )
    }

    if (registrationData.password.length < 6) {
      return NextResponse.json(
        { error: 'Password must be at least 6 characters long' },
        { status: 400 }
      )
    }

    // Complete the registration
    const result = await userDraftService.completeRegistration(token, registrationData)

    if (!result.success) {
      return NextResponse.json(
        { error: result.message },
        { status: 400 }
      )
    }

    return NextResponse.json({
      success: true,
      message: result.message
    })

  } catch (error: any) {
    console.error('Error in complete registration API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
