import { NextRequest, NextResponse } from 'next/server'
import { userDraftService } from '@/lib/user-draft-service'
import { supabase } from '@/lib/supabase'

export async function GET(request: NextRequest) {
  try {
    // Verify the user is authenticated
    const authHeader = request.headers.get('authorization')
    if (!authHeader) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Extract the token from the Authorization header
    const token = authHeader.replace('Bearer ', '')
    
    // Verify the token with Supabase
    const { data: { user }, error: authError } = await supabase.auth.getUser(token)
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Invalid authentication token' },
        { status: 401 }
      )
    }

    // Get organization filter from query params
    const { searchParams } = new URL(request.url)
    const organizationId = searchParams.get('organization_id')

    // List drafts
    const drafts = await userDraftService.listDrafts(organizationId || undefined)

    return NextResponse.json({
      success: true,
      drafts
    })

  } catch (error: any) {
    console.error('Error in list drafts API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
