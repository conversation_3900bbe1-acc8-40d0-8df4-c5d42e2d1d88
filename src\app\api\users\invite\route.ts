import { NextRequest, NextResponse } from 'next/server'
import { userDraftService, type CreateUserDraftData } from '@/lib/user-draft-service'
import { supabase } from '@/lib/supabase'

export async function POST(request: NextRequest) {
  try {
    // Verify the user is authenticated
    const authHeader = request.headers.get('authorization')
    if (!authHeader) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Extract the token from the Authorization header
    const token = authHeader.replace('Bearer ', '')

    // Verify the token with Supabase
    const { data: { user }, error: authError } = await supabase.auth.getUser(token)

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Invalid authentication token' },
        { status: 401 }
      )
    }

    // Parse request body
    const body = await request.json()
    const draftData: CreateUserDraftData = {
      email: body.email,
      full_name: body.full_name,
      account_number: body.account_number,
      job_duties: body.job_duties,
      organization_id: body.organization_id,
      facility_id: body.facility_id,
      is_active: body.is_active ?? true
    }

    // Validate required fields
    if (!draftData.email || !draftData.full_name || !draftData.organization_id) {
      return NextResponse.json(
        { error: 'Email, full name, and organization are required' },
        { status: 400 }
      )
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(draftData.email)) {
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      )
    }

    // TODO: Add permission check here
    // For now, we'll allow any authenticated user to invite others
    // In a real app, you'd check if the user has admin/manager permissions

    // Create the draft invitation
    const result = await userDraftService.createDraftUser(draftData)

    if (!result.success) {
      return NextResponse.json(
        { error: result.message },
        { status: 400 }
      )
    }

    return NextResponse.json({
      success: true,
      message: result.message,
      draft_id: result.draft_id,
      signup_url: result.signup_url
    })

  } catch (error: any) {
    console.error('Error in user invitation API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
