'use client'

import React, { useState, useEffect } from 'react'
import { Modal } from '@/components/ui/Modal'
import { Button } from '@/components/ui/button'
import { supabase, type Facility, type Organization } from '@/lib/supabase'
import toast from 'react-hot-toast'

export interface FacilityFormProps {
  isOpen: boolean
  onClose: () => void
  onSuccess: () => void
  facility?: Facility | null
  organizations: Organization[]
  mode: 'create' | 'edit'
}

export function FacilityForm({
  isOpen,
  onClose,
  onSuccess,
  facility,
  organizations,
  mode
}: FacilityFormProps) {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    address: '',
    organization_id: ''
  })
  const [loading, setLoading] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})

  useEffect(() => {
    if (facility && mode === 'edit') {
      setFormData({
        name: facility.name || '',
        description: facility.description || '',
        address: facility.address || '',
        organization_id: facility.organization_id || ''
      })
    } else {
      setFormData({
        name: '',
        description: '',
        address: '',
        organization_id: organizations.length > 0 ? organizations[0].id : ''
      })
    }
    setErrors({})
  }, [facility, mode, isOpen, organizations])

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.name.trim()) {
      newErrors.name = 'Facility name is required'
    }

    if (!formData.organization_id) {
      newErrors.organization_id = 'Organization is required'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    setLoading(true)

    try {
      if (mode === 'create') {
        const { error } = await supabase
          .from('facilities')
          .insert([{
            name: formData.name.trim(),
            description: formData.description.trim() || null,
            address: formData.address.trim() || null,
            organization_id: formData.organization_id
          }])

        if (error) throw error
        toast.success('Facility created successfully')
      } else {
        const { error } = await supabase
          .from('facilities')
          .update({
            name: formData.name.trim(),
            description: formData.description.trim() || null,
            address: formData.address.trim() || null,
            organization_id: formData.organization_id
          })
          .eq('id', facility!.id)

        if (error) throw error
        toast.success('Facility updated successfully')
      }

      onSuccess()
      onClose()
    } catch (error: any) {
      toast.error(`Error ${mode === 'create' ? 'creating' : 'updating'} facility: ${error.message}`)
    } finally {
      setLoading(false)
    }
  }

  const handleClose = React.useCallback(() => {
    if (!loading) {
      onClose()
    }
  }, [loading, onClose])

  const handleOrganizationChange = React.useCallback((e: React.ChangeEvent<HTMLSelectElement>) => {
    setFormData(prev => ({ ...prev, organization_id: e.target.value }))
  }, [])

  const handleNameChange = React.useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({ ...prev, name: e.target.value }))
  }, [])

  const handleDescriptionChange = React.useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setFormData(prev => ({ ...prev, description: e.target.value }))
  }, [])

  const handleAddressChange = React.useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setFormData(prev => ({ ...prev, address: e.target.value }))
  }, [])

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title={mode === 'create' ? 'Create Facility' : 'Edit Facility'}
      size="md"
    >
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label htmlFor="organization_id" className="block text-sm font-medium text-gray-700">
            Organization *
          </label>
          <select
            id="organization_id"
            value={formData.organization_id}
            onChange={handleOrganizationChange}
            className={`mt-1 block w-full rounded-md border px-3 py-2 shadow-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 ${
              errors.organization_id ? 'border-red-300' : 'border-gray-300'
            }`}
            disabled={loading}
          >
            <option value="">Select an organization</option>
            {organizations.map((org) => (
              <option key={org.id} value={org.id}>
                {org.name}
              </option>
            ))}
          </select>
          {errors.organization_id && (
            <p className="mt-1 text-sm text-red-600">{errors.organization_id}</p>
          )}
        </div>

        <div>
          <label htmlFor="name" className="block text-sm font-medium text-gray-700">
            Facility Name *
          </label>
          <input
            type="text"
            id="name"
            value={formData.name}
            onChange={handleNameChange}
            className={`mt-1 block w-full rounded-md border px-3 py-2 shadow-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 ${
              errors.name ? 'border-red-300' : 'border-gray-300'
            }`}
            placeholder="Enter facility name"
            disabled={loading}
          />
          {errors.name && (
            <p className="mt-1 text-sm text-red-600">{errors.name}</p>
          )}
        </div>

        <div>
          <label htmlFor="description" className="block text-sm font-medium text-gray-700">
            Description
          </label>
          <textarea
            id="description"
            rows={3}
            value={formData.description}
            onChange={handleDescriptionChange}
            className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
            placeholder="Enter facility description (optional)"
            disabled={loading}
          />
        </div>

        <div>
          <label htmlFor="address" className="block text-sm font-medium text-gray-700">
            Address
          </label>
          <textarea
            id="address"
            rows={2}
            value={formData.address}
            onChange={handleAddressChange}
            className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
            placeholder="Enter facility address (optional)"
            disabled={loading}
          />
        </div>

        <div className="flex justify-end space-x-3 pt-4">
          <Button
            type="button"
            variant="outline"
            onClick={handleClose}
            disabled={loading}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={loading}
          >
            {loading ? 'Saving...' : mode === 'create' ? 'Create Facility' : 'Update Facility'}
          </Button>
        </div>
      </form>
    </Modal>
  )
}
