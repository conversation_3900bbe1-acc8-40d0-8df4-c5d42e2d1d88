'use client'

import React, { useState, useEffect } from 'react'
import { Modal } from '@/components/ui/Modal'
import { Button } from '@/components/ui/button'
import { DataTable } from '@/components/ui/data-table'
import { inspectionService } from '@/lib/inspection-service'
import { type InspectionCategory, type Organization } from '@/lib/supabase'
import toast from 'react-hot-toast'
import { PlusIcon, PencilIcon, TrashIcon } from '@heroicons/react/24/outline'

export interface InspectionCategoryFormProps {
  isOpen: boolean
  onClose: () => void
  onSuccess: () => void
  category?: InspectionCategory | null
  organizations: Organization[]
  mode: 'create' | 'edit'
}

export function InspectionCategoryForm({
  isOpen,
  onClose,
  onSuccess,
  category,
  organizations,
  mode
}: InspectionCategoryFormProps) {
  const [loading, setLoading] = useState(false)
  const [categories, setCategories] = useState<InspectionCategory[]>([])
  const [showForm, setShowForm] = useState(false)
  const [editingCategory, setEditingCategory] = useState<InspectionCategory | null>(null)
  const [formMode, setFormMode] = useState<'create' | 'edit'>('create')
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    organization_id: ''
  })

  useEffect(() => {
    if (isOpen) {
      fetchCategories()
      if (mode === 'edit' && category) {
        setEditingCategory(category)
        setFormMode('edit')
        setFormData({
          name: category.name,
          description: category.description || '',
          organization_id: category.organization_id
        })
        setShowForm(true)
      } else {
        resetForm()
      }
    }
  }, [isOpen, mode, category])

  const fetchCategories = async () => {
    try {
      const data = await inspectionService.getCategories()
      setCategories(data)
    } catch (error: any) {
      toast.error(`Error loading categories: ${error.message}`)
    }
  }

  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      organization_id: ''
    })
    setErrors({})
    setEditingCategory(null)
    setFormMode('create')
    setShowForm(false)
  }

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.name.trim()) {
      newErrors.name = 'Category name is required'
    }

    if (!formData.organization_id) {
      newErrors.organization_id = 'Organization is required'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    setLoading(true)

    try {
      if (formMode === 'create') {
        await inspectionService.createCategory({
          name: formData.name.trim(),
          description: formData.description.trim() || undefined,
          organization_id: formData.organization_id
        })
        toast.success('Category created successfully')
      } else {
        await inspectionService.updateCategory(editingCategory!.id, {
          name: formData.name.trim(),
          description: formData.description.trim() || undefined,
          organization_id: formData.organization_id
        })
        toast.success('Category updated successfully')
      }

      await fetchCategories()
      resetForm()
      onSuccess()
    } catch (error: any) {
      toast.error(`Error ${formMode === 'create' ? 'creating' : 'updating'} category: ${error.message}`)
    } finally {
      setLoading(false)
    }
  }

  const handleCreateCategory = () => {
    resetForm()
    setFormMode('create')
    setShowForm(true)
  }

  const handleEditCategory = (cat: InspectionCategory) => {
    setEditingCategory(cat)
    setFormMode('edit')
    setFormData({
      name: cat.name,
      description: cat.description || '',
      organization_id: cat.organization_id
    })
    setShowForm(true)
  }

  const handleDeleteCategory = async (cat: InspectionCategory) => {
    if (!confirm('Are you sure you want to delete this category?')) {
      return
    }

    try {
      await inspectionService.deleteCategory(cat.id)
      toast.success('Category deleted successfully')
      await fetchCategories()
      onSuccess()
    } catch (error: any) {
      toast.error(`Error deleting category: ${error.message}`)
    }
  }

  const handleClose = () => {
    resetForm()
    onClose()
  }

  const handleNameChange = React.useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({ ...prev, name: e.target.value }))
  }, [])

  const handleDescriptionChange = React.useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setFormData(prev => ({ ...prev, description: e.target.value }))
  }, [])

  const handleOrganizationChange = React.useCallback((e: React.ChangeEvent<HTMLSelectElement>) => {
    setFormData(prev => ({ ...prev, organization_id: e.target.value }))
  }, [])

  const columns = [
    {
      key: 'name',
      label: 'Name',
      sortable: true
    },
    {
      key: 'description',
      label: 'Description',
      sortable: false
    },
    {
      key: 'organization',
      label: 'Organization',
      sortable: true,
      render: (cat: InspectionCategory) => cat.organization?.name || 'N/A'
    },
    {
      key: 'created_at',
      label: 'Created',
      sortable: true,
      render: (cat: InspectionCategory) => new Date(cat.created_at).toLocaleDateString()
    }
  ]

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title="Manage Inspection Categories"
      size="xl"
    >
      <div className="space-y-6">
        {!showForm ? (
          <>
            <div className="flex items-center justify-between">
              <p className="text-sm text-gray-600">
                Manage categories for organizing inspection points
              </p>
              <Button
                onClick={handleCreateCategory}
                className="flex items-center space-x-2"
              >
                <PlusIcon className="h-4 w-4" />
                <span>Create Category</span>
              </Button>
            </div>

            <DataTable
              data={categories}
              columns={columns}
              rowActions={[
                {
                  label: 'Edit',
                  icon: PencilIcon,
                  onClick: handleEditCategory
                },
                {
                  label: 'Delete',
                  icon: TrashIcon,
                  onClick: handleDeleteCategory
                }
              ]}
            />
          </>
        ) : (
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium text-gray-900">
                {formMode === 'create' ? 'Create Category' : 'Edit Category'}
              </h3>
              <Button
                type="button"
                variant="outline"
                onClick={() => setShowForm(false)}
                disabled={loading}
              >
                Back to List
              </Button>
            </div>

            <div>
              <label htmlFor="organization_id" className="block text-sm font-medium text-gray-700">
                Organization *
              </label>
              <select
                id="organization_id"
                value={formData.organization_id}
                onChange={handleOrganizationChange}
                className={`mt-1 block w-full rounded-md border px-3 py-2 shadow-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 ${
                  errors.organization_id ? 'border-red-300' : 'border-gray-300'
                }`}
                disabled={loading}
              >
                <option value="">Select an organization</option>
                {organizations.map((org) => (
                  <option key={org.id} value={org.id}>
                    {org.name}
                  </option>
                ))}
              </select>
              {errors.organization_id && (
                <p className="mt-1 text-sm text-red-600">{errors.organization_id}</p>
              )}
            </div>

            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                Category Name *
              </label>
              <input
                type="text"
                id="name"
                value={formData.name}
                onChange={handleNameChange}
                className={`mt-1 block w-full rounded-md border px-3 py-2 shadow-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 ${
                  errors.name ? 'border-red-300' : 'border-gray-300'
                }`}
                placeholder="Enter category name"
                disabled={loading}
              />
              {errors.name && (
                <p className="mt-1 text-sm text-red-600">{errors.name}</p>
              )}
            </div>

            <div>
              <label htmlFor="description" className="block text-sm font-medium text-gray-700">
                Description
              </label>
              <textarea
                id="description"
                rows={3}
                value={formData.description}
                onChange={handleDescriptionChange}
                className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                placeholder="Enter category description (optional)"
                disabled={loading}
              />
            </div>

            <div className="flex justify-end space-x-3 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => setShowForm(false)}
                disabled={loading}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={loading}
              >
                {loading 
                  ? (formMode === 'create' ? 'Creating...' : 'Updating...') 
                  : (formMode === 'create' ? 'Create Category' : 'Update Category')
                }
              </Button>
            </div>
          </form>
        )}

        <div className="flex justify-end pt-4 border-t">
          <Button
            type="button"
            variant="outline"
            onClick={handleClose}
          >
            Close
          </Button>
        </div>
      </div>
    </Modal>
  )
}
