'use client'

import React, { useState, useEffect } from 'react'
import { Modal } from '@/components/ui/Modal'
import { Button } from '@/components/ui/button'
import { ImageUpload } from '@/components/ui/ImageUpload'
import { inspectionService } from '@/lib/inspection-service'
import { type InspectionPoint, type InspectionCategory, type Organization, type Facility, type Workplace } from '@/lib/supabase'
import toast from 'react-hot-toast'

export interface InspectionPointFormProps {
  isOpen: boolean
  onClose: () => void
  onSuccess: () => void
  inspectionPoint?: InspectionPoint | null
  organizations: Organization[]
  facilities: Facility[]
  workplaces: Workplace[]
  categories: InspectionCategory[]
  mode: 'create' | 'edit'
}

export function InspectionPointForm({
  isOpen,
  onClose,
  onSuccess,
  inspectionPoint,
  organizations,
  facilities,
  workplaces,
  categories,
  mode
}: InspectionPointFormProps) {
  const [loading, setLoading] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [formData, setFormData] = useState({
    inspection_name: '',
    description: '',
    image_url: '',
    workplace_id: '',
    organization_id: '',
    facility_id: '',
    category_id: ''
  })

  useEffect(() => {
    if (mode === 'edit' && inspectionPoint) {
      setFormData({
        inspection_name: inspectionPoint.inspection_name,
        description: inspectionPoint.description || '',
        image_url: inspectionPoint.image_url || '',
        workplace_id: inspectionPoint.workplace_id,
        organization_id: inspectionPoint.organization_id,
        facility_id: inspectionPoint.facility_id,
        category_id: inspectionPoint.category_id
      })
    } else {
      setFormData({
        inspection_name: '',
        description: '',
        image_url: '',
        workplace_id: '',
        organization_id: '',
        facility_id: '',
        category_id: ''
      })
    }
    setErrors({})
  }, [mode, inspectionPoint, isOpen])

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.inspection_name.trim()) {
      newErrors.inspection_name = 'Inspection name is required'
    }

    if (!formData.organization_id) {
      newErrors.organization_id = 'Organization is required'
    }

    if (!formData.facility_id) {
      newErrors.facility_id = 'Facility is required'
    }

    if (!formData.workplace_id) {
      newErrors.workplace_id = 'Workplace is required'
    }

    if (!formData.category_id) {
      newErrors.category_id = 'Category is required'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    setLoading(true)

    try {
      if (mode === 'create') {
        await inspectionService.createInspectionPoint({
          inspection_name: formData.inspection_name.trim(),
          description: formData.description.trim() || undefined,
          image_url: formData.image_url.trim() || undefined,
          workplace_id: formData.workplace_id,
          organization_id: formData.organization_id,
          facility_id: formData.facility_id,
          category_id: formData.category_id
        })
        toast.success('Inspection point created successfully')
      } else {
        await inspectionService.updateInspectionPoint(inspectionPoint!.id, {
          inspection_name: formData.inspection_name.trim(),
          description: formData.description.trim() || undefined,
          image_url: formData.image_url.trim() || undefined,
          workplace_id: formData.workplace_id,
          organization_id: formData.organization_id,
          facility_id: formData.facility_id,
          category_id: formData.category_id
        })
        toast.success('Inspection point updated successfully')
      }

      onSuccess()
      onClose()
    } catch (error: any) {
      toast.error(`Error ${mode === 'create' ? 'creating' : 'updating'} inspection point: ${error.message}`)
    } finally {
      setLoading(false)
    }
  }

  const handleClose = React.useCallback(() => {
    if (!loading) {
      onClose()
    }
  }, [loading, onClose])

  const handleOrganizationChange = React.useCallback((e: React.ChangeEvent<HTMLSelectElement>) => {
    setFormData(prev => ({ 
      ...prev, 
      organization_id: e.target.value,
      facility_id: '',
      workplace_id: ''
    }))
  }, [])

  const handleFacilityChange = React.useCallback((e: React.ChangeEvent<HTMLSelectElement>) => {
    setFormData(prev => ({ 
      ...prev, 
      facility_id: e.target.value,
      workplace_id: ''
    }))
  }, [])

  const handleWorkplaceChange = React.useCallback((e: React.ChangeEvent<HTMLSelectElement>) => {
    setFormData(prev => ({ ...prev, workplace_id: e.target.value }))
  }, [])

  const handleCategoryChange = React.useCallback((e: React.ChangeEvent<HTMLSelectElement>) => {
    setFormData(prev => ({ ...prev, category_id: e.target.value }))
  }, [])

  const handleNameChange = React.useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({ ...prev, inspection_name: e.target.value }))
  }, [])

  const handleDescriptionChange = React.useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setFormData(prev => ({ ...prev, description: e.target.value }))
  }, [])

  const handleImageUrlChange = React.useCallback((url: string) => {
    setFormData(prev => ({ ...prev, image_url: url }))
  }, [])

  const filteredFacilities = facilities.filter(facility => 
    !formData.organization_id || facility.organization_id === formData.organization_id
  )

  const filteredWorkplaces = workplaces.filter(workplace => 
    !formData.facility_id || workplace.facility_id === formData.facility_id
  )

  const filteredCategories = categories.filter(category =>
    !formData.organization_id || category.organization_id === formData.organization_id
  )

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title={mode === 'create' ? 'Create Inspection Point' : 'Edit Inspection Point'}
      size="lg"
    >
      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label htmlFor="organization_id" className="block text-sm font-medium text-gray-700">
              Organization *
            </label>
            <select
              id="organization_id"
              value={formData.organization_id}
              onChange={handleOrganizationChange}
              className={`mt-1 block w-full rounded-md border px-3 py-2 shadow-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 ${
                errors.organization_id ? 'border-red-300' : 'border-gray-300'
              }`}
              disabled={loading}
            >
              <option value="">Select an organization</option>
              {organizations.map((org) => (
                <option key={org.id} value={org.id}>
                  {org.name}
                </option>
              ))}
            </select>
            {errors.organization_id && (
              <p className="mt-1 text-sm text-red-600">{errors.organization_id}</p>
            )}
          </div>

          <div>
            <label htmlFor="facility_id" className="block text-sm font-medium text-gray-700">
              Facility *
            </label>
            <select
              id="facility_id"
              value={formData.facility_id}
              onChange={handleFacilityChange}
              className={`mt-1 block w-full rounded-md border px-3 py-2 shadow-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 ${
                errors.facility_id ? 'border-red-300' : 'border-gray-300'
              }`}
              disabled={loading || !formData.organization_id}
            >
              <option value="">Select a facility</option>
              {filteredFacilities.map((facility) => (
                <option key={facility.id} value={facility.id}>
                  {facility.name}
                </option>
              ))}
            </select>
            {errors.facility_id && (
              <p className="mt-1 text-sm text-red-600">{errors.facility_id}</p>
            )}
          </div>

          <div>
            <label htmlFor="workplace_id" className="block text-sm font-medium text-gray-700">
              Workplace *
            </label>
            <select
              id="workplace_id"
              value={formData.workplace_id}
              onChange={handleWorkplaceChange}
              className={`mt-1 block w-full rounded-md border px-3 py-2 shadow-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 ${
                errors.workplace_id ? 'border-red-300' : 'border-gray-300'
              }`}
              disabled={loading || !formData.facility_id}
            >
              <option value="">Select a workplace</option>
              {filteredWorkplaces.map((workplace) => (
                <option key={workplace.id} value={workplace.id}>
                  {workplace.name}
                </option>
              ))}
            </select>
            {errors.workplace_id && (
              <p className="mt-1 text-sm text-red-600">{errors.workplace_id}</p>
            )}
          </div>

          <div>
            <label htmlFor="category_id" className="block text-sm font-medium text-gray-700">
              Category *
            </label>
            <select
              id="category_id"
              value={formData.category_id}
              onChange={handleCategoryChange}
              className={`mt-1 block w-full rounded-md border px-3 py-2 shadow-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 ${
                errors.category_id ? 'border-red-300' : 'border-gray-300'
              }`}
              disabled={loading}
            >
              <option value="">Select a category</option>
              {filteredCategories.map((category) => (
                <option key={category.id} value={category.id}>
                  {category.name}
                </option>
              ))}
            </select>
            {errors.category_id && (
              <p className="mt-1 text-sm text-red-600">{errors.category_id}</p>
            )}
          </div>
        </div>

        <div>
          <label htmlFor="inspection_name" className="block text-sm font-medium text-gray-700">
            Inspection Name *
          </label>
          <input
            type="text"
            id="inspection_name"
            value={formData.inspection_name}
            onChange={handleNameChange}
            className={`mt-1 block w-full rounded-md border px-3 py-2 shadow-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 ${
              errors.inspection_name ? 'border-red-300' : 'border-gray-300'
            }`}
            placeholder="Enter inspection point name"
            disabled={loading}
          />
          {errors.inspection_name && (
            <p className="mt-1 text-sm text-red-600">{errors.inspection_name}</p>
          )}
        </div>

        <div>
          <label htmlFor="description" className="block text-sm font-medium text-gray-700">
            Description
          </label>
          <textarea
            id="description"
            rows={3}
            value={formData.description}
            onChange={handleDescriptionChange}
            className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
            placeholder="Enter inspection point description (optional)"
            disabled={loading}
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Image
          </label>
          <ImageUpload
            value={formData.image_url}
            onChange={handleImageUrlChange}
            disabled={loading}
          />
        </div>

        <div className="flex justify-end space-x-3 pt-4">
          <Button
            type="button"
            variant="outline"
            onClick={handleClose}
            disabled={loading}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={loading}
          >
            {loading 
              ? (mode === 'create' ? 'Creating...' : 'Updating...') 
              : (mode === 'create' ? 'Create Inspection Point' : 'Update Inspection Point')
            }
          </Button>
        </div>
      </form>
    </Modal>
  )
}
