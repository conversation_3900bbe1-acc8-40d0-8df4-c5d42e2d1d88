'use client'

import React, { useState, useEffect } from 'react'
import { Modal } from '@/components/ui/Modal'
import { Button } from '@/components/ui/button'
import { supabase, type Organization } from '@/lib/supabase'
import toast from 'react-hot-toast'

export interface OrganizationFormProps {
  isOpen: boolean
  onClose: () => void
  onSuccess: () => void
  organization?: Organization | null
  mode: 'create' | 'edit'
}

export function OrganizationForm({
  isOpen,
  onClose,
  onSuccess,
  organization,
  mode
}: OrganizationFormProps) {
  const [formData, setFormData] = useState({
    name: '',
    description: ''
  })
  const [loading, setLoading] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})

  useEffect(() => {
    if (organization && mode === 'edit') {
      setFormData({
        name: organization.name || '',
        description: organization.description || ''
      })
    } else {
      setFormData({
        name: '',
        description: ''
      })
    }
    setErrors({})
  }, [organization, mode, isOpen])

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.name.trim()) {
      newErrors.name = 'Organization name is required'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    setLoading(true)

    try {
      if (mode === 'create') {
        const { error } = await supabase
          .from('organizations')
          .insert([{
            name: formData.name.trim(),
            description: formData.description.trim() || null
          }])

        if (error) throw error
        toast.success('Organization created successfully')
      } else {
        const { error } = await supabase
          .from('organizations')
          .update({
            name: formData.name.trim(),
            description: formData.description.trim() || null
          })
          .eq('id', organization!.id)

        if (error) throw error
        toast.success('Organization updated successfully')
      }

      onSuccess()
      onClose()
    } catch (error: any) {
      toast.error(`Error ${mode === 'create' ? 'creating' : 'updating'} organization: ${error.message}`)
    } finally {
      setLoading(false)
    }
  }

  const handleClose = React.useCallback(() => {
    if (!loading) {
      onClose()
    }
  }, [loading, onClose])

  const handleNameChange = React.useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({ ...prev, name: e.target.value }))
  }, [])

  const handleDescriptionChange = React.useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setFormData(prev => ({ ...prev, description: e.target.value }))
  }, [])

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title={mode === 'create' ? 'Create Organization' : 'Edit Organization'}
      size="md"
    >
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label htmlFor="name" className="block text-sm font-medium text-gray-700">
            Organization Name *
          </label>
          <input
            type="text"
            id="name"
            value={formData.name}
            onChange={handleNameChange}
            className={`mt-1 block w-full rounded-md border px-3 py-2 shadow-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 ${
              errors.name ? 'border-red-300' : 'border-gray-300'
            }`}
            placeholder="Enter organization name"
            disabled={loading}
          />
          {errors.name && (
            <p className="mt-1 text-sm text-red-600">{errors.name}</p>
          )}
        </div>

        <div>
          <label htmlFor="description" className="block text-sm font-medium text-gray-700">
            Description
          </label>
          <textarea
            id="description"
            rows={3}
            value={formData.description}
            onChange={handleDescriptionChange}
            className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
            placeholder="Enter organization description (optional)"
            disabled={loading}
          />
        </div>

        <div className="flex justify-end space-x-3 pt-4">
          <Button
            type="button"
            variant="outline"
            onClick={handleClose}
            disabled={loading}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={loading}
          >
            {loading ? 'Saving...' : mode === 'create' ? 'Create Organization' : 'Update Organization'}
          </Button>
        </div>
      </form>
    </Modal>
  )
}
