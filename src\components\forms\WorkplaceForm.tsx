'use client'

import React, { useState, useEffect } from 'react'
import { Modal } from '@/components/ui/Modal'
import { Button } from '@/components/ui/button'
import { supabase, type Workplace, type Facility } from '@/lib/supabase'
import toast from 'react-hot-toast'

export interface WorkplaceFormProps {
  isOpen: boolean
  onClose: () => void
  onSuccess: () => void
  workplace?: Workplace | null
  facilities: Facility[]
  mode: 'create' | 'edit'
  selectedFacilityId?: string
}

export function WorkplaceForm({
  isOpen,
  onClose,
  onSuccess,
  workplace,
  facilities,
  mode,
  selectedFacilityId
}: WorkplaceFormProps) {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    location: '',
    facility_id: ''
  })
  const [loading, setLoading] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})

  useEffect(() => {
    if (workplace && mode === 'edit') {
      setFormData({
        name: workplace.name || '',
        description: workplace.description || '',
        location: workplace.location || '',
        facility_id: workplace.facility_id || ''
      })
    } else {
      setFormData({
        name: '',
        description: '',
        location: '',
        facility_id: selectedFacilityId || (facilities.length > 0 ? facilities[0].id : '')
      })
    }
    setErrors({})
  }, [workplace, mode, isOpen, facilities, selectedFacilityId])

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.name.trim()) {
      newErrors.name = 'Workplace name is required'
    }

    if (!formData.facility_id) {
      newErrors.facility_id = 'Facility is required'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    setLoading(true)

    try {
      if (mode === 'create') {
        const { error } = await supabase
          .from('workplaces')
          .insert([{
            name: formData.name.trim(),
            description: formData.description.trim() || null,
            location: formData.location.trim() || null,
            facility_id: formData.facility_id
          }])

        if (error) throw error
        toast.success('Workplace created successfully')
      } else {
        const { error } = await supabase
          .from('workplaces')
          .update({
            name: formData.name.trim(),
            description: formData.description.trim() || null,
            location: formData.location.trim() || null,
            facility_id: formData.facility_id
          })
          .eq('id', workplace!.id)

        if (error) throw error
        toast.success('Workplace updated successfully')
      }

      onSuccess()
      onClose()
    } catch (error: any) {
      toast.error(`Error ${mode === 'create' ? 'creating' : 'updating'} workplace: ${error.message}`)
    } finally {
      setLoading(false)
    }
  }

  const handleClose = React.useCallback(() => {
    if (!loading) {
      onClose()
    }
  }, [loading, onClose])

  const handleFacilityChange = React.useCallback((e: React.ChangeEvent<HTMLSelectElement>) => {
    setFormData(prev => ({ ...prev, facility_id: e.target.value }))
  }, [])

  const handleNameChange = React.useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({ ...prev, name: e.target.value }))
  }, [])

  const handleDescriptionChange = React.useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setFormData(prev => ({ ...prev, description: e.target.value }))
  }, [])

  const handleLocationChange = React.useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({ ...prev, location: e.target.value }))
  }, [])

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title={mode === 'create' ? 'Create Workplace' : 'Edit Workplace'}
      size="md"
    >
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label htmlFor="facility_id" className="block text-sm font-medium text-gray-700">
            Facility *
          </label>
          <select
            id="facility_id"
            value={formData.facility_id}
            onChange={handleFacilityChange}
            className={`mt-1 block w-full rounded-md border px-3 py-2 shadow-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 ${
              errors.facility_id ? 'border-red-300' : 'border-gray-300'
            }`}
            disabled={loading}
          >
            <option value="">Select a facility</option>
            {facilities.map((facility) => (
              <option key={facility.id} value={facility.id}>
                {facility.name}
              </option>
            ))}
          </select>
          {errors.facility_id && (
            <p className="mt-1 text-sm text-red-600">{errors.facility_id}</p>
          )}
        </div>

        <div>
          <label htmlFor="name" className="block text-sm font-medium text-gray-700">
            Workplace Name *
          </label>
          <input
            type="text"
            id="name"
            value={formData.name}
            onChange={handleNameChange}
            className={`mt-1 block w-full rounded-md border px-3 py-2 shadow-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 ${
              errors.name ? 'border-red-300' : 'border-gray-300'
            }`}
            placeholder="Enter workplace name"
            disabled={loading}
          />
          {errors.name && (
            <p className="mt-1 text-sm text-red-600">{errors.name}</p>
          )}
        </div>

        <div>
          <label htmlFor="description" className="block text-sm font-medium text-gray-700">
            Description
          </label>
          <textarea
            id="description"
            rows={3}
            value={formData.description}
            onChange={handleDescriptionChange}
            className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
            placeholder="Enter workplace description (optional)"
            disabled={loading}
          />
        </div>

        <div>
          <label htmlFor="location" className="block text-sm font-medium text-gray-700">
            Location
          </label>
          <input
            type="text"
            id="location"
            value={formData.location}
            onChange={handleLocationChange}
            className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
            placeholder="Enter workplace location (optional)"
            disabled={loading}
          />
        </div>

        <div className="flex justify-end space-x-3 pt-4">
          <Button
            type="button"
            variant="outline"
            onClick={handleClose}
            disabled={loading}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={loading}
          >
            {loading ? 'Saving...' : mode === 'create' ? 'Create Workplace' : 'Update Workplace'}
          </Button>
        </div>
      </form>
    </Modal>
  )
}
