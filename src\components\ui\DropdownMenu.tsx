'use client'

import React, { useState, useRef, useEffect } from 'react'
import { EllipsisHorizontalIcon } from '@heroicons/react/24/outline'

export interface DropdownMenuItem {
  label: string
  onClick: () => void
  icon?: React.ComponentType<{ className?: string }>
  variant?: 'default' | 'primary' | 'secondary' | 'danger'
  disabled?: boolean
}

export interface DropdownMenuProps {
  items: DropdownMenuItem[]
  className?: string
  trigger?: React.ReactNode
}

export function DropdownMenu({ items, className = '', trigger }: DropdownMenuProps) {
  const [isOpen, setIsOpen] = useState(false)
  const menuRef = useRef<HTMLDivElement>(null)
  const buttonRef = useRef<HTMLButtonElement>(null)

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside)
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [isOpen])

  const handleItemClick = (item: DropdownMenuItem) => {
    if (!item.disabled) {
      item.onClick()
      setIsOpen(false)
    }
  }

  const defaultTrigger = (
    <button
      ref={buttonRef}
      type="button"
      className="inline-flex items-center justify-center w-8 h-8 text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 rounded-md"
      onClick={() => setIsOpen(!isOpen)}
    >
      <EllipsisHorizontalIcon className="w-5 h-5" />
    </button>
  )

  return (
    <div className={`relative inline-block text-left ${className}`} ref={menuRef}>
      {trigger ? (
        <div onClick={() => setIsOpen(!isOpen)}>
          {trigger}
        </div>
      ) : (
        defaultTrigger
      )}

      {isOpen && (
        <div className="absolute right-0 z-50 mt-2 w-48 origin-top-right bg-white border border-gray-200 rounded-md shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
          <div className="py-1">
            {items.map((item, index) => (
              <button
                key={index}
                className={`${
                  item.disabled
                    ? 'text-gray-400 cursor-not-allowed'
                    : item.variant === 'danger'
                    ? 'text-red-700 hover:bg-red-50'
                    : item.variant === 'primary'
                    ? 'text-orange-700 hover:bg-orange-50'
                    : 'text-gray-700 hover:bg-gray-50'
                } group flex items-center w-full px-4 py-2 text-sm text-left`}
                onClick={() => handleItemClick(item)}
                disabled={item.disabled}
              >
                {item.icon && (
                  <item.icon className="mr-3 w-4 h-4" />
                )}
                {item.label}
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}
