import { supabase } from './supabase'

export interface UploadResult {
  url: string
  path: string
}

export class ImageUploadService {
  private static readonly BUCKET_NAME = 'inspection-images'
  private static readonly MAX_FILE_SIZE = 5 * 1024 * 1024 // 5MB
  private static readonly ALLOWED_TYPES = ['image/jpeg', 'image/png', 'image/webp', 'image/gif']

  static async uploadImage(file: File, folder: string = 'inspection-points'): Promise<UploadResult> {
    // Validate file
    this.validateFile(file)

    // Generate unique filename
    const fileExt = file.name.split('.').pop()
    const fileName = `${folder}/${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExt}`

    try {
      // Upload file to Supabase Storage
      const { data, error } = await supabase.storage
        .from(this.BUCKET_NAME)
        .upload(fileName, file, {
          cacheControl: '3600',
          upsert: false
        })

      if (error) {
        throw new Error(`Upload failed: ${error.message}`)
      }

      // Get public URL
      const { data: urlData } = supabase.storage
        .from(this.BUCKET_NAME)
        .getPublicUrl(fileName)

      return {
        url: urlData.publicUrl,
        path: fileName
      }
    } catch (error: any) {
      throw new Error(`Image upload failed: ${error.message}`)
    }
  }

  static async deleteImage(path: string): Promise<void> {
    try {
      const { error } = await supabase.storage
        .from(this.BUCKET_NAME)
        .remove([path])

      if (error) {
        throw new Error(`Delete failed: ${error.message}`)
      }
    } catch (error: any) {
      throw new Error(`Image deletion failed: ${error.message}`)
    }
  }

  static validateFile(file: File): void {
    if (!file) {
      throw new Error('No file provided')
    }

    if (file.size > this.MAX_FILE_SIZE) {
      throw new Error(`File size must be less than ${this.MAX_FILE_SIZE / 1024 / 1024}MB`)
    }

    if (!this.ALLOWED_TYPES.includes(file.type)) {
      throw new Error(`File type must be one of: ${this.ALLOWED_TYPES.join(', ')}`)
    }
  }

  static getImageUrl(path: string): string {
    const { data } = supabase.storage
      .from(this.BUCKET_NAME)
      .getPublicUrl(path)
    
    return data.publicUrl
  }
}
