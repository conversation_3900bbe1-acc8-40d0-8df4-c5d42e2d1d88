import { supabase, type InspectionCategory, type InspectionPoint, type WorkplaceInspection, type InspectionResult } from './supabase'

export interface CreateInspectionCategoryData {
  name: string
  description?: string
  organization_id: string
}

export interface CreateInspectionPointData {
  inspection_name: string
  description?: string
  image_url?: string
  workplace_id: string
  organization_id: string
  facility_id: string
  category_id: string
}

export interface CreateWorkplaceInspectionData {
  workplace_id: string
  notes?: string
}

export interface CreateInspectionResultData {
  workplace_inspection_id: string
  inspection_point_id: string
  is_compliant: boolean
  notes?: string
}

export const inspectionService = {
  // Inspection Categories
  async getCategories(organizationId?: string): Promise<InspectionCategory[]> {
    try {
      let query = supabase
        .from('inspection_categories')
        .select(`
          *,
          organization:organizations(id, name)
        `)
        .order('name')

      if (organizationId) {
        query = query.eq('organization_id', organizationId)
      }

      const { data, error } = await query

      if (error) throw error
      return data || []
    } catch (error) {
      console.error('Error fetching inspection categories:', error)
      return []
    }
  },

  async createCategory(categoryData: CreateInspectionCategoryData): Promise<InspectionCategory | null> {
    try {
      // Get current user
      const { data: { user }, error: userError } = await supabase.auth.getUser()
      if (userError || !user) {
        throw new Error('User not authenticated')
      }

      const { data, error } = await supabase
        .from('inspection_categories')
        .insert({
          ...categoryData,
          created_by: user.id
        })
        .select(`
          *,
          organization:organizations(id, name)
        `)
        .single()

      if (error) throw error
      return data
    } catch (error) {
      console.error('Error creating inspection category:', error)
      throw error
    }
  },

  async updateCategory(id: string, updates: Partial<CreateInspectionCategoryData>): Promise<InspectionCategory | null> {
    try {
      const { data, error } = await supabase
        .from('inspection_categories')
        .update(updates)
        .eq('id', id)
        .select(`
          *,
          organization:organizations(id, name)
        `)
        .single()

      if (error) throw error
      return data
    } catch (error) {
      console.error('Error updating inspection category:', error)
      throw error
    }
  },

  async deleteCategory(id: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('inspection_categories')
        .delete()
        .eq('id', id)

      if (error) throw error
    } catch (error) {
      console.error('Error deleting inspection category:', error)
      throw error
    }
  },

  // Inspection Points
  async getInspectionPoints(filters?: {
    organizationId?: string
    facilityId?: string
    workplaceId?: string
    categoryId?: string
    isActive?: boolean
  }): Promise<InspectionPoint[]> {
    try {
      let query = supabase
        .from('inspection_points')
        .select(`
          *,
          workplace:workplaces(id, name),
          organization:organizations(id, name),
          facility:facilities(id, name),
          category:inspection_categories(id, name)
        `)
        .order('inspection_name')

      if (filters?.organizationId) {
        query = query.eq('organization_id', filters.organizationId)
      }
      if (filters?.facilityId) {
        query = query.eq('facility_id', filters.facilityId)
      }
      if (filters?.workplaceId) {
        query = query.eq('workplace_id', filters.workplaceId)
      }
      if (filters?.categoryId) {
        query = query.eq('category_id', filters.categoryId)
      }
      if (filters?.isActive !== undefined) {
        query = query.eq('is_active', filters.isActive)
      }

      const { data, error } = await query

      if (error) throw error
      return data || []
    } catch (error) {
      console.error('Error fetching inspection points:', error)
      return []
    }
  },

  async createInspectionPoint(pointData: CreateInspectionPointData): Promise<InspectionPoint | null> {
    try {
      // Get current user
      const { data: { user }, error: userError } = await supabase.auth.getUser()
      if (userError || !user) {
        throw new Error('User not authenticated')
      }

      const { data, error } = await supabase
        .from('inspection_points')
        .insert({
          ...pointData,
          created_by: user.id
        })
        .select(`
          *,
          workplace:workplaces(id, name),
          organization:organizations(id, name),
          facility:facilities(id, name),
          category:inspection_categories(id, name)
        `)
        .single()

      if (error) throw error
      return data
    } catch (error) {
      console.error('Error creating inspection point:', error)
      throw error
    }
  },

  async updateInspectionPoint(id: string, updates: Partial<CreateInspectionPointData>): Promise<InspectionPoint | null> {
    try {
      const { data, error } = await supabase
        .from('inspection_points')
        .update(updates)
        .eq('id', id)
        .select(`
          *,
          workplace:workplaces(id, name),
          organization:organizations(id, name),
          facility:facilities(id, name),
          category:inspection_categories(id, name)
        `)
        .single()

      if (error) throw error
      return data
    } catch (error) {
      console.error('Error updating inspection point:', error)
      throw error
    }
  },

  async deleteInspectionPoint(id: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('inspection_points')
        .delete()
        .eq('id', id)

      if (error) throw error
    } catch (error) {
      console.error('Error deleting inspection point:', error)
      throw error
    }
  },

  // Workplace Inspections
  async getWorkplaceInspections(filters?: {
    workplaceId?: string
    inspectorUserId?: string
    status?: string
  }): Promise<WorkplaceInspection[]> {
    try {
      let query = supabase
        .from('workplace_inspections')
        .select(`
          *,
          workplace:workplaces(id, name)
        `)
        .order('inspection_date', { ascending: false })

      if (filters?.workplaceId) {
        query = query.eq('workplace_id', filters.workplaceId)
      }
      if (filters?.inspectorUserId) {
        query = query.eq('inspector_user_id', filters.inspectorUserId)
      }
      if (filters?.status) {
        query = query.eq('status', filters.status)
      }

      const { data, error } = await query

      if (error) throw error
      return data || []
    } catch (error) {
      console.error('Error fetching workplace inspections:', error)
      return []
    }
  },

  async createWorkplaceInspection(inspectionData: CreateWorkplaceInspectionData): Promise<WorkplaceInspection | null> {
    try {
      // Get current user
      const { data: { user }, error: userError } = await supabase.auth.getUser()
      if (userError || !user) {
        throw new Error('User not authenticated')
      }

      const { data, error } = await supabase
        .from('workplace_inspections')
        .insert({
          ...inspectionData,
          inspector_user_id: user.id
        })
        .select(`
          *,
          workplace:workplaces(id, name)
        `)
        .single()

      if (error) throw error
      return data
    } catch (error) {
      console.error('Error creating workplace inspection:', error)
      throw error
    }
  },

  async updateWorkplaceInspection(id: string, updates: Partial<CreateWorkplaceInspectionData & { status: string }>): Promise<WorkplaceInspection | null> {
    try {
      const { data, error } = await supabase
        .from('workplace_inspections')
        .update(updates)
        .eq('id', id)
        .select(`
          *,
          workplace:workplaces(id, name)
        `)
        .single()

      if (error) throw error
      return data
    } catch (error) {
      console.error('Error updating workplace inspection:', error)
      throw error
    }
  },

  // Inspection Results
  async getInspectionResults(workplaceInspectionId: string): Promise<InspectionResult[]> {
    try {
      const { data, error } = await supabase
        .from('inspection_results')
        .select(`
          *,
          inspection_point:inspection_points(id, inspection_name, description, image_url)
        `)
        .eq('workplace_inspection_id', workplaceInspectionId)
        .order('checked_at')

      if (error) throw error
      return data || []
    } catch (error) {
      console.error('Error fetching inspection results:', error)
      return []
    }
  },

  async createInspectionResult(resultData: CreateInspectionResultData): Promise<InspectionResult | null> {
    try {
      const { data, error } = await supabase
        .from('inspection_results')
        .insert(resultData)
        .select(`
          *,
          inspection_point:inspection_points(id, inspection_name, description, image_url)
        `)
        .single()

      if (error) throw error
      return data
    } catch (error) {
      console.error('Error creating inspection result:', error)
      throw error
    }
  },

  async updateInspectionResult(id: string, updates: Partial<CreateInspectionResultData>): Promise<InspectionResult | null> {
    try {
      const { data, error } = await supabase
        .from('inspection_results')
        .update(updates)
        .eq('id', id)
        .select(`
          *,
          inspection_point:inspection_points(id, inspection_name, description, image_url)
        `)
        .single()

      if (error) throw error
      return data
    } catch (error) {
      console.error('Error updating inspection result:', error)
      throw error
    }
  }
}
