import { supabase, type UserDraft } from './supabase'

export interface CreateUserDraftData {
  email: string
  full_name: string
  account_number?: string
  job_duties?: string
  organization_id: string
  facility_id?: string
  is_active?: boolean
}

export interface DraftInvitationResult {
  success: boolean
  message: string
  draft_id?: string
  signup_url?: string
}

export interface CompleteRegistrationData {
  password: string
  full_name?: string
  account_number?: string
  job_duties?: string
}

export const userDraftService = {
  /**
   * Create a draft user invitation
   */
  async createDraftUser(draftData: CreateUserDraftData): Promise<DraftInvitationResult> {
    try {
      // Check if there's already an active invitation for this email
      const { data: existingDraft, error: checkError } = await supabase
        .from('user_drafts')
        .select('id, email, expires_at')
        .eq('email', draftData.email.toLowerCase())
        .is('registered_at', null)
        .gt('expires_at', new Date().toISOString())
        .maybeSingle()

      if (checkError && checkError.code !== 'PGRST116') {
        throw checkError
      }

      if (existingDraft) {
        return {
          success: false,
          message: `An active invitation already exists for ${draftData.email}. Please wait for it to expire or cancel it first.`
        }
      }

      // Check if user already exists in auth.users
      const { data: existingUser, error: userCheckError } = await supabase
        .from('users')
        .select('id, email')
        .eq('email', draftData.email.toLowerCase())
        .maybeSingle()

      if (userCheckError && userCheckError.code !== 'PGRST116') {
        throw userCheckError
      }

      if (existingUser) {
        return {
          success: false,
          message: `A user with email ${draftData.email} already exists in the system.`
        }
      }

      // Create the draft user
      const { data: draft, error: createError } = await supabase
        .from('user_drafts')
        .insert({
          email: draftData.email.toLowerCase(),
          full_name: draftData.full_name,
          account_number: draftData.account_number || null,
          job_duties: draftData.job_duties || null,
          organization_id: draftData.organization_id,
          facility_id: draftData.facility_id || null,
          is_active: draftData.is_active ?? true
        })
        .select()
        .single()

      if (createError) {
        throw createError
      }

      // Generate signup URL
      const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'
      const signupUrl = `${baseUrl}/signup/${draft.signup_token}`

      // Mark invitation as sent
      await supabase
        .from('user_drafts')
        .update({ invitation_sent_at: new Date().toISOString() })
        .eq('id', draft.id)

      return {
        success: true,
        message: 'User invitation created successfully. Send the signup URL to the user.',
        draft_id: draft.id,
        signup_url: signupUrl
      }

    } catch (error: any) {
      console.error('Error creating draft user:', error)
      return {
        success: false,
        message: `Failed to create invitation: ${error.message}`
      }
    }
  },

  /**
   * Get draft user by signup token
   */
  async getDraftByToken(token: string): Promise<UserDraft | null> {
    try {
      const { data: draft, error } = await supabase
        .from('user_drafts')
        .select(`
          *,
          organization:organizations(id, name),
          facility:facilities(id, name)
        `)
        .eq('signup_token', token)
        .is('registered_at', null)
        .gt('expires_at', new Date().toISOString())
        .single()

      if (error) {
        if (error.code === 'PGRST116') {
          return null // No matching record
        }
        throw error
      }

      return draft as UserDraft
    } catch (error) {
      console.error('Error fetching draft by token:', error)
      return null
    }
  },

  /**
   * Complete user registration from draft
   */
  async completeRegistration(
    token: string, 
    registrationData: CompleteRegistrationData
  ): Promise<DraftInvitationResult> {
    try {
      // Get the draft
      const draft = await this.getDraftByToken(token)
      if (!draft) {
        return {
          success: false,
          message: 'Invalid or expired invitation link.'
        }
      }

      // Create the actual user account
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email: draft.email,
        password: registrationData.password,
        options: {
          data: {
            full_name: registrationData.full_name || draft.full_name,
            invited_by_organization: draft.organization_id
          }
        }
      })

      if (authError) {
        throw authError
      }

      if (!authData.user) {
        throw new Error('Failed to create user account')
      }

      // Create user profile
      const { error: profileError } = await supabase
        .from('users')
        .insert({
          id: authData.user.id,
          email: draft.email,
          full_name: registrationData.full_name || draft.full_name,
          account_number: registrationData.account_number || draft.account_number,
          job_duties: registrationData.job_duties || draft.job_duties,
          is_active: draft.is_active
        })

      if (profileError) {
        // Try to clean up the auth user if profile creation fails
        try {
          await supabase.auth.admin.deleteUser(authData.user.id)
        } catch (cleanupError) {
          console.error('Failed to cleanup auth user:', cleanupError)
        }
        throw profileError
      }

      // Create user role
      const { error: roleError } = await supabase
        .from('user_roles')
        .insert({
          user_id: authData.user.id,
          organization_id: draft.organization_id,
          facility_id: draft.facility_id,
          role: 'member'
        })

      if (roleError) {
        console.warn('User created but role assignment failed:', roleError)
      }

      // Mark draft as completed
      await supabase
        .from('user_drafts')
        .update({
          registered_at: new Date().toISOString(),
          registered_user_id: authData.user.id
        })
        .eq('id', draft.id)

      return {
        success: true,
        message: 'Registration completed successfully! You can now sign in.'
      }

    } catch (error: any) {
      console.error('Error completing registration:', error)
      return {
        success: false,
        message: `Registration failed: ${error.message}`
      }
    }
  },

  /**
   * List draft users for an organization
   */
  async listDrafts(organizationId?: string): Promise<UserDraft[]> {
    try {
      let query = supabase
        .from('user_drafts')
        .select(`
          *,
          organization:organizations(id, name),
          facility:facilities(id, name)
        `)
        .is('registered_at', null)
        .order('created_at', { ascending: false })

      if (organizationId) {
        query = query.eq('organization_id', organizationId)
      }

      const { data: drafts, error } = await query

      if (error) {
        throw error
      }

      return drafts as UserDraft[]
    } catch (error) {
      console.error('Error listing drafts:', error)
      return []
    }
  },

  /**
   * Cancel/delete a draft invitation
   */
  async cancelDraft(draftId: string): Promise<DraftInvitationResult> {
    try {
      const { error } = await supabase
        .from('user_drafts')
        .delete()
        .eq('id', draftId)
        .is('registered_at', null)

      if (error) {
        throw error
      }

      return {
        success: true,
        message: 'Invitation cancelled successfully.'
      }
    } catch (error: any) {
      return {
        success: false,
        message: `Failed to cancel invitation: ${error.message}`
      }
    }
  },

  /**
   * Resend invitation (update expiration and get new URL)
   */
  async resendInvitation(draftId: string): Promise<DraftInvitationResult> {
    try {
      const { data: draft, error } = await supabase
        .from('user_drafts')
        .update({
          expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days from now
          invitation_sent_at: new Date().toISOString()
        })
        .eq('id', draftId)
        .is('registered_at', null)
        .select()
        .single()

      if (error) {
        throw error
      }

      const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'
      const signupUrl = `${baseUrl}/signup/${draft.signup_token}`

      return {
        success: true,
        message: 'Invitation resent successfully.',
        signup_url: signupUrl
      }
    } catch (error: any) {
      return {
        success: false,
        message: `Failed to resend invitation: ${error.message}`
      }
    }
  }
}
