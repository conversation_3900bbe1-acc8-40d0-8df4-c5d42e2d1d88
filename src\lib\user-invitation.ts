import { supabaseAdmin } from './supabase'

export interface InviteUserData {
  email: string
  full_name: string
  account_number?: string
  job_duties?: string
  organization_id: string
  is_active?: boolean
}

export interface InvitationResult {
  success: boolean
  message: string
  user_id?: string
}

export const userInvitationService = {
  /**
   * Invite a new user through Supabase Auth
   * This creates an auth user and sends an invitation email
   */
  async inviteUser(userData: InviteUserData): Promise<InvitationResult> {
    if (!supabaseAdmin) {
      return {
        success: false,
        message: 'Admin client not configured. Please set SUPABASE_SERVICE_ROLE_KEY environment variable.'
      }
    }

    try {
      // Step 1: Create auth user through admin API
      const { data: authData, error: authError } = await supabaseAdmin.auth.admin.inviteUserByEmail(
        userData.email,
        {
          data: {
            full_name: userData.full_name,
            invited_by_organization: userData.organization_id
          },
          redirectTo: `${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'}/auth/accept-invitation`
        }
      )

      if (authError) {
        console.error('Auth invitation error:', authError)
        return {
          success: false,
          message: `Failed to send invitation: ${authError.message}`
        }
      }

      if (!authData.user) {
        return {
          success: false,
          message: 'Failed to create user account'
        }
      }

      // Step 2: Create user profile in database
      const { error: profileError } = await supabaseAdmin
        .from('users')
        .insert({
          id: authData.user.id,
          email: userData.email,
          full_name: userData.full_name,
          account_number: userData.account_number || null,
          job_duties: userData.job_duties || null,
          is_active: userData.is_active ?? true
        })

      if (profileError) {
        console.error('Profile creation error:', profileError)
        // Try to clean up the auth user if profile creation fails
        try {
          await supabaseAdmin.auth.admin.deleteUser(authData.user.id)
        } catch (cleanupError) {
          console.error('Failed to cleanup auth user:', cleanupError)
        }
        
        return {
          success: false,
          message: `Failed to create user profile: ${profileError.message}`
        }
      }

      // Step 3: Create user role assignment
      const { error: roleError } = await supabaseAdmin
        .from('user_roles')
        .insert({
          user_id: authData.user.id,
          organization_id: userData.organization_id,
          role: 'member' // Default role
        })

      if (roleError) {
        console.error('Role assignment error:', roleError)
        // Note: We don't fail the entire process if role assignment fails
        // as this can be fixed later by an admin
        console.warn('User created but role assignment failed. Admin should assign role manually.')
      }

      return {
        success: true,
        message: 'User invitation sent successfully. They will receive an email with instructions to complete their registration.',
        user_id: authData.user.id
      }

    } catch (error: any) {
      console.error('Unexpected error during user invitation:', error)
      return {
        success: false,
        message: `Unexpected error: ${error.message || 'Please try again.'}`
      }
    }
  },

  /**
   * Resend invitation to an existing user
   */
  async resendInvitation(email: string): Promise<InvitationResult> {
    if (!supabaseAdmin) {
      return {
        success: false,
        message: 'Admin client not configured. Please set SUPABASE_SERVICE_ROLE_KEY environment variable.'
      }
    }

    try {
      const { data, error } = await supabaseAdmin.auth.admin.inviteUserByEmail(
        email,
        {
          redirectTo: `${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'}/auth/accept-invitation`
        }
      )

      if (error) {
        return {
          success: false,
          message: `Failed to resend invitation: ${error.message}`
        }
      }

      return {
        success: true,
        message: 'Invitation resent successfully.'
      }

    } catch (error: any) {
      return {
        success: false,
        message: `Unexpected error: ${error.message || 'Please try again.'}`
      }
    }
  },

  /**
   * Update user profile (for edit operations)
   */
  async updateUserProfile(userId: string, updates: Partial<InviteUserData>): Promise<InvitationResult> {
    if (!supabaseAdmin) {
      return {
        success: false,
        message: 'Admin client not configured.'
      }
    }

    try {
      // Update user profile
      const { error: profileError } = await supabaseAdmin
        .from('users')
        .update({
          full_name: updates.full_name,
          account_number: updates.account_number || null,
          job_duties: updates.job_duties || null,
          is_active: updates.is_active
        })
        .eq('id', userId)

      if (profileError) {
        return {
          success: false,
          message: `Failed to update user: ${profileError.message}`
        }
      }

      // Update organization role if changed
      if (updates.organization_id) {
        const { error: roleError } = await supabaseAdmin
          .from('user_roles')
          .update({
            organization_id: updates.organization_id
          })
          .eq('user_id', userId)

        if (roleError) {
          console.warn('Failed to update user role:', roleError)
        }
      }

      return {
        success: true,
        message: 'User updated successfully.'
      }

    } catch (error: any) {
      return {
        success: false,
        message: `Unexpected error: ${error.message || 'Please try again.'}`
      }
    }
  }
}
