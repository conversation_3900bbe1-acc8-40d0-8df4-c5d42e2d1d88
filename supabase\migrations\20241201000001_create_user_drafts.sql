-- Create user_drafts table for invitation system
CREATE TABLE IF NOT EXISTS user_drafts (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    email VARCHAR(255) NOT NULL,
    full_name VARCHAR(255) NOT NULL,
    account_number VARCHAR(100),
    job_duties TEXT,
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    facility_id UUID REFERENCES facilities(id) ON DELETE SET NULL,
    signup_token UUID DEFAULT gen_random_uuid() NOT NULL UNIQUE,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT (NOW() + INTERVAL '7 days'),
    created_by UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT true,
    invitation_sent_at TIMESTAMP WITH TIME ZONE,
    registered_at TIMESTAMP WITH TIME ZONE,
    registered_user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_user_drafts_email ON user_drafts(email);
CREATE INDEX IF NOT EXISTS idx_user_drafts_signup_token ON user_drafts(signup_token);
CREATE INDEX IF NOT EXISTS idx_user_drafts_organization_id ON user_drafts(organization_id);
CREATE INDEX IF NOT EXISTS idx_user_drafts_expires_at ON user_drafts(expires_at);
CREATE INDEX IF NOT EXISTS idx_user_drafts_created_by ON user_drafts(created_by);

-- Create unique constraint to prevent duplicate invitations for same email
CREATE UNIQUE INDEX IF NOT EXISTS idx_user_drafts_email_active 
ON user_drafts(email) 
WHERE registered_at IS NULL AND expires_at > NOW();

-- Add RLS policies
ALTER TABLE user_drafts ENABLE ROW LEVEL SECURITY;

-- Policy: Users can only see drafts they created or drafts for organizations they belong to
CREATE POLICY "Users can view relevant user drafts" ON user_drafts
    FOR SELECT USING (
        created_by = auth.uid() OR
        organization_id IN (
            SELECT ur.organization_id 
            FROM user_roles ur 
            WHERE ur.user_id = auth.uid()
        )
    );

-- Policy: Users can create drafts for organizations they belong to
CREATE POLICY "Users can create user drafts" ON user_drafts
    FOR INSERT WITH CHECK (
        organization_id IN (
            SELECT ur.organization_id 
            FROM user_roles ur 
            WHERE ur.user_id = auth.uid()
        )
    );

-- Policy: Users can update drafts they created
CREATE POLICY "Users can update their user drafts" ON user_drafts
    FOR UPDATE USING (created_by = auth.uid());

-- Policy: Users can delete drafts they created
CREATE POLICY "Users can delete their user drafts" ON user_drafts
    FOR DELETE USING (created_by = auth.uid());

-- Function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_user_drafts_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to update updated_at on changes
CREATE TRIGGER trigger_user_drafts_updated_at
    BEFORE UPDATE ON user_drafts
    FOR EACH ROW
    EXECUTE FUNCTION update_user_drafts_updated_at();

-- Function to clean up expired drafts (can be called by a cron job)
CREATE OR REPLACE FUNCTION cleanup_expired_user_drafts()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM user_drafts 
    WHERE expires_at < NOW() 
    AND registered_at IS NULL;
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant necessary permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON user_drafts TO authenticated;
GRANT USAGE ON SCHEMA public TO authenticated;
