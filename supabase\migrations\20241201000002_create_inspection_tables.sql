-- Create inspection system tables
-- This migration creates the inspection_categories and inspection_points tables

-- Create inspection_categories table
CREATE TABLE IF NOT EXISTS inspection_categories (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    created_by UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create inspection_points table
CREATE TABLE IF NOT EXISTS inspection_points (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    inspection_name VARCHAR(255) NOT NULL,
    description TEXT,
    image_url TEXT,
    workplace_id UUID NOT NULL REFERENCES workplaces(id) ON DELETE CASCADE,
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    facility_id UUID NOT NULL REFERENCES facilities(id) ON DELETE CASCADE,
    category_id UUID NOT NULL REFERENCES inspection_categories(id) ON DELETE CASCADE,
    created_by UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT true
);

-- Create workplace_inspections table
CREATE TABLE IF NOT EXISTS workplace_inspections (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    workplace_id UUID NOT NULL REFERENCES workplaces(id) ON DELETE CASCADE,
    inspector_user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    inspection_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    status VARCHAR(50) DEFAULT 'in_progress' CHECK (status IN ('in_progress', 'completed', 'failed')),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create inspection_results table
CREATE TABLE IF NOT EXISTS inspection_results (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    workplace_inspection_id UUID NOT NULL REFERENCES workplace_inspections(id) ON DELETE CASCADE,
    inspection_point_id UUID NOT NULL REFERENCES inspection_points(id) ON DELETE CASCADE,
    is_compliant BOOLEAN NOT NULL,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(workplace_inspection_id, inspection_point_id)
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_inspection_categories_organization_id ON inspection_categories(organization_id);
CREATE INDEX IF NOT EXISTS idx_inspection_categories_created_by ON inspection_categories(created_by);
CREATE INDEX IF NOT EXISTS idx_inspection_points_workplace_id ON inspection_points(workplace_id);
CREATE INDEX IF NOT EXISTS idx_inspection_points_organization_id ON inspection_points(organization_id);
CREATE INDEX IF NOT EXISTS idx_inspection_points_facility_id ON inspection_points(facility_id);
CREATE INDEX IF NOT EXISTS idx_inspection_points_category_id ON inspection_points(category_id);
CREATE INDEX IF NOT EXISTS idx_inspection_points_created_by ON inspection_points(created_by);
CREATE INDEX IF NOT EXISTS idx_workplace_inspections_workplace_id ON workplace_inspections(workplace_id);
CREATE INDEX IF NOT EXISTS idx_workplace_inspections_inspector_user_id ON workplace_inspections(inspector_user_id);
CREATE INDEX IF NOT EXISTS idx_inspection_results_workplace_inspection_id ON inspection_results(workplace_inspection_id);
CREATE INDEX IF NOT EXISTS idx_inspection_results_inspection_point_id ON inspection_results(inspection_point_id);

-- Enable Row Level Security
ALTER TABLE inspection_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE inspection_points ENABLE ROW LEVEL SECURITY;
ALTER TABLE workplace_inspections ENABLE ROW LEVEL SECURITY;
ALTER TABLE inspection_results ENABLE ROW LEVEL SECURITY;

-- RLS Policies for inspection_categories
CREATE POLICY "Users can view categories in their organizations" ON inspection_categories
    FOR SELECT USING (
        organization_id IN (
            SELECT ur.organization_id 
            FROM user_roles ur 
            WHERE ur.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can create categories in their organizations" ON inspection_categories
    FOR INSERT WITH CHECK (
        organization_id IN (
            SELECT ur.organization_id 
            FROM user_roles ur 
            WHERE ur.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update categories they created" ON inspection_categories
    FOR UPDATE USING (
        created_by = auth.uid() OR
        organization_id IN (
            SELECT ur.organization_id 
            FROM user_roles ur 
            WHERE ur.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can delete categories they created" ON inspection_categories
    FOR DELETE USING (
        created_by = auth.uid() OR
        organization_id IN (
            SELECT ur.organization_id 
            FROM user_roles ur 
            WHERE ur.user_id = auth.uid()
        )
    );

-- RLS Policies for inspection_points
CREATE POLICY "Users can view inspection points in their organizations" ON inspection_points
    FOR SELECT USING (
        organization_id IN (
            SELECT ur.organization_id 
            FROM user_roles ur 
            WHERE ur.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can create inspection points in their organizations" ON inspection_points
    FOR INSERT WITH CHECK (
        organization_id IN (
            SELECT ur.organization_id 
            FROM user_roles ur 
            WHERE ur.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update inspection points in their organizations" ON inspection_points
    FOR UPDATE USING (
        organization_id IN (
            SELECT ur.organization_id 
            FROM user_roles ur 
            WHERE ur.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can delete inspection points in their organizations" ON inspection_points
    FOR DELETE USING (
        organization_id IN (
            SELECT ur.organization_id 
            FROM user_roles ur 
            WHERE ur.user_id = auth.uid()
        )
    );

-- RLS Policies for workplace_inspections
CREATE POLICY "Users can view workplace inspections in their organizations" ON workplace_inspections
    FOR SELECT USING (
        workplace_id IN (
            SELECT w.id FROM workplaces w
            JOIN facilities f ON f.id = w.facility_id
            JOIN user_roles ur ON ur.organization_id = f.organization_id
            WHERE ur.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can create workplace inspections in their organizations" ON workplace_inspections
    FOR INSERT WITH CHECK (
        workplace_id IN (
            SELECT w.id FROM workplaces w
            JOIN facilities f ON f.id = w.facility_id
            JOIN user_roles ur ON ur.organization_id = f.organization_id
            WHERE ur.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update workplace inspections they created" ON workplace_inspections
    FOR UPDATE USING (
        inspector_user_id = auth.uid() OR
        workplace_id IN (
            SELECT w.id FROM workplaces w
            JOIN facilities f ON f.id = w.facility_id
            JOIN user_roles ur ON ur.organization_id = f.organization_id
            WHERE ur.user_id = auth.uid()
        )
    );

-- RLS Policies for inspection_results
CREATE POLICY "Users can view inspection results in their organizations" ON inspection_results
    FOR SELECT USING (
        workplace_inspection_id IN (
            SELECT wi.id FROM workplace_inspections wi
            JOIN workplaces w ON w.id = wi.workplace_id
            JOIN facilities f ON f.id = w.facility_id
            JOIN user_roles ur ON ur.organization_id = f.organization_id
            WHERE ur.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can create inspection results in their organizations" ON inspection_results
    FOR INSERT WITH CHECK (
        workplace_inspection_id IN (
            SELECT wi.id FROM workplace_inspections wi
            JOIN workplaces w ON w.id = wi.workplace_id
            JOIN facilities f ON f.id = w.facility_id
            JOIN user_roles ur ON ur.organization_id = f.organization_id
            WHERE ur.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update inspection results in their organizations" ON inspection_results
    FOR UPDATE USING (
        workplace_inspection_id IN (
            SELECT wi.id FROM workplace_inspections wi
            JOIN workplaces w ON w.id = wi.workplace_id
            JOIN facilities f ON f.id = w.facility_id
            JOIN user_roles ur ON ur.organization_id = f.organization_id
            WHERE ur.user_id = auth.uid()
        )
    );

-- Create triggers for updated_at timestamps
CREATE TRIGGER update_inspection_categories_updated_at 
    BEFORE UPDATE ON inspection_categories
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_inspection_points_updated_at 
    BEFORE UPDATE ON inspection_points
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_workplace_inspections_updated_at 
    BEFORE UPDATE ON workplace_inspections
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_inspection_results_updated_at 
    BEFORE UPDATE ON inspection_results
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Grant necessary permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON inspection_categories TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON inspection_points TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON workplace_inspections TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON inspection_results TO authenticated;
