-- Create storage bucket for inspection images
-- This migration creates the storage bucket and policies for image uploads

-- Create the inspection-images bucket
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
    'inspection-images',
    'inspection-images',
    true,
    5242880, -- 5MB limit
    ARRAY['image/jpeg', 'image/png', 'image/webp', 'image/gif']
) ON CONFLICT (id) DO NOTHING;

-- Enable RLS on storage.objects
ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;

-- Policy: Users can view images in their organizations
CREATE POLICY "Users can view inspection images in their organizations" ON storage.objects
    FOR SELECT USING (
        bucket_id = 'inspection-images' AND
        auth.uid() IS NOT NULL AND
        EXISTS (
            SELECT 1 FROM user_roles ur
            WHERE ur.user_id = auth.uid()
        )
    );

-- Policy: Users can upload images to their organizations
CREATE POLICY "Users can upload inspection images" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'inspection-images' AND
        auth.uid() IS NOT NULL AND
        EXISTS (
            SELECT 1 FROM user_roles ur
            WHERE ur.user_id = auth.uid()
        )
    );

-- Policy: Users can update images they uploaded
CREATE POLICY "Users can update their inspection images" ON storage.objects
    FOR UPDATE USING (
        bucket_id = 'inspection-images' AND
        auth.uid() = owner
    );

-- Policy: Users can delete images they uploaded
CREATE POLICY "Users can delete their inspection images" ON storage.objects
    FOR DELETE USING (
        bucket_id = 'inspection-images' AND
        auth.uid() = owner
    );
